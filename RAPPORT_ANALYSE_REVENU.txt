RAPPORT D'ANALYSE CRISP-DM - REVENU
============================================================
Date: 2025-06-21 15:00:46

=== ANALYSE DU DATASET REVENU ===
Dimensions: (48842, 15)
Variable cible: income
Distribution cible:
income
<=50K    37155
>50K     11687
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
                age        fnlwgt  ...  capital-loss  hours-per-week
count  48842.000000  4.884200e+04  ...  48842.000000    48842.000000
mean      38.643585  1.896641e+05  ...     87.502314       40.422382
std       13.710510  1.056040e+05  ...    403.004552       12.391444
min       17.000000  1.228500e+04  ...      0.000000        1.000000
25%       28.000000  1.175505e+05  ...      0.000000       40.000000
50%       37.000000  1.781445e+05  ...      0.000000       40.000000
75%       48.000000  2.376420e+05  ...      0.000000       45.000000
max       90.000000  1.490400e+06  ...   4356.000000       99.000000

[8 rows x 6 columns]

=== INDICATEURS DE POSITION ET DISPERSION ===
age:
  IQR: 20.000, Asymétrie: 0.558, Aplatissement: -0.184
fnlwgt:
  IQR: 120091.500, Asymétrie: 1.439, Aplatissement: 6.058
educational-num:
  IQR: 3.000, Asymétrie: -0.317, Aplatissement: 0.626
capital-gain:
  IQR: 0.000, Asymétrie: 11.895, Aplatissement: 152.693
capital-loss:
  IQR: 0.000, Asymétrie: 4.570, Aplatissement: 20.014
hours-per-week:
  IQR: 5.000, Asymétrie: 0.239, Aplatissement: 2.951

=== VALEURS MANQUANTES ===
workclass         2799
occupation        2809
native-country     857
dtype: int64

Variables numériques: ['age', 'fnlwgt', 'educational-num', 'capital-gain', 'capital-loss', 'hours-per-week']
Variables catégorielles: ['workclass', 'education', 'marital-status', 'occupation', 'relationship', 'race', 'gender', 'native-country']

=== DÉTECTION D'OUTLIERS (méthode IQR) ===
age: 216 outliers (0.4%)
fnlwgt: 1453 outliers (3.0%)
educational-num: 1794 outliers (3.7%)
capital-gain: 4035 outliers (8.3%)
capital-loss: 2282 outliers (4.7%)
hours-per-week: 13496 outliers (27.6%)

=== CORRÉLATIONS AVEC LA CIBLE ===
educational-num: 0.333
age: 0.230
hours-per-week: 0.228
capital-gain: 0.223
capital-loss: 0.148

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 6 variables
Imputation catégorielle (mode) pour 8 variables
One-Hot Encoding pour workclass: 7 nouvelles variables
Label Encoding pour education
One-Hot Encoding pour marital-status: 6 nouvelles variables
Label Encoding pour occupation
One-Hot Encoding pour relationship: 5 nouvelles variables
One-Hot Encoding pour race: 4 nouvelles variables
One-Hot Encoding pour gender: 1 nouvelles variables
Label Encoding pour native-country
StandardScaler appliqué à 32 variables
Split 70/30: Train=(34189, 32), Test=(14653, 32)
SMOTE appliqué: 52016 échantillons équilibrés

=== SUGGESTIONS DE FEATURE ENGINEERING ===
1. Transformation log pour 'fnlwgt' (asymétrie: 1.44)
2. Transformation log pour 'capital-gain' (asymétrie: 11.89)
3. Transformation log pour 'capital-loss' (asymétrie: 4.57)
4. Traitement outliers pour 'capital-gain' (8.3%)
5. Traitement outliers pour 'hours-per-week' (27.6%)
6. Regroupement de catégories pour 'native-country' (41 modalités)

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8130 | Acc: 0.8013 | Over: 0.0190
Decision Tree        | F1: 0.8112 | Acc: 0.8084 | Over: 0.1915
Random Forest        | F1: 0.8489 | Acc: 0.8480 | Over: 0.1519
KNN                  | F1: 0.7987 | Acc: 0.7881 | Over: 0.1174
SVM                  | F1: 0.8063 | Acc: 0.7936 | Over: 0.0490
Naive Bayes          | F1: 0.6432 | Acc: 0.6199 | Over: 0.1166
Gradient Boosting    | F1: 0.8405 | Acc: 0.8333 | Over: 0.0322

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Random Forest        | F1-CV: 0.8997 (±0.0012)
Gradient Boosting    | F1-CV: 0.8638 (±0.0013)
Logistic Regression  | F1-CV: 0.8196 (±0.0020)

--- Validation croisée 7-folds ---
Random Forest        | F1-CV: 0.9012 (±0.0014)
Gradient Boosting    | F1-CV: 0.8628 (±0.0026)
Logistic Regression  | F1-CV: 0.8192 (±0.0031)

--- Validation croisée 10-folds ---
Random Forest        | F1-CV: 0.9025 (±0.0010)
Gradient Boosting    | F1-CV: 0.8632 (±0.0031)
Logistic Regression  | F1-CV: 0.8195 (±0.0032)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Random Forest        0.8489   0.8480   0.9022   0.1519      
Gradient Boosting    0.8405   0.8333   0.9154   0.0322      
Logistic Regression  0.8130   0.8013   0.8994   0.0190      
Decision Tree        0.8112   0.8084   0.7525   0.1915      
SVM                  0.8063   0.7936   0.8975   0.0490      
KNN                  0.7987   0.7881   0.8451   0.1174      
Naive Bayes          0.6432   0.6199   0.8608   0.1166      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Random Forest
Performance F1: 0.8489

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Random Forest

              precision    recall  f1-score   support

           0       0.90      0.89      0.90     11147
           1       0.68      0.70      0.69      3506

    accuracy                           0.85     14653
   macro avg       0.79      0.80      0.79     14653
weighted avg       0.85      0.85      0.85     14653


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.0968
✅ Pas d'overfitting significatif détecté

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Random Forest
   - Performance F1: 0.8489
   - Accuracy: 0.8480

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
