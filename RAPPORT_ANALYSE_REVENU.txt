RAPPORT D'ANALYSE CRISP-DM - REVENU
============================================================
Date: 2025-06-21 16:09:31

=== ANALYSE DU DATASET REVENU ===
Dimensions: (10000, 15)
Variable cible: income
Distribution cible:
income
<=50K    7658
>50K     2342
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
               age        fnlwgt  ...  capital-loss  hours-per-week
count  10000.00000  1.000000e+04  ...  10000.000000    10000.000000
mean      38.72140  1.897136e+05  ...     80.158500       40.407600
std       13.65596  1.064721e+05  ...    388.835421       12.110125
min       17.00000  1.882700e+04  ...      0.000000        1.000000
25%       28.00000  1.168238e+05  ...      0.000000       40.000000
50%       37.00000  1.785715e+05  ...      0.000000       40.000000
75%       48.00000  2.368182e+05  ...      0.000000       45.000000
max       90.00000  1.490400e+06  ...   4356.000000       99.000000

[8 rows x 6 columns]

=== INDICATEURS DE POSITION ET DISPERSION ===
age: IQR: 20.000, Asymétrie: 0.555, Aplatissement: -0.178
fnlwgt: IQR: 119994.500, Asymétrie: 1.642, Aplatissement: 8.998
educational-num: IQR: 3.000, Asymétrie: -0.304, Aplatissement: 0.563
capital-gain: IQR: 0.000, Asymétrie: 11.858, Aplatissement: 152.013
capital-loss: IQR: 0.000, Asymétrie: 4.873, Aplatissement: 23.330
hours-per-week: IQR: 5.000, Asymétrie: 0.186, Aplatissement: 2.844

=== VALEURS MANQUANTES ===
workclass         552
occupation        553
native-country    193
dtype: int64

Variables numériques: ['age', 'fnlwgt', 'educational-num', 'capital-gain', 'capital-loss', 'hours-per-week']
Variables catégorielles: ['workclass', 'education', 'marital-status', 'occupation', 'relationship', 'race', 'gender', 'native-country']

=== DÉTECTION D'OUTLIERS (méthode IQR) ===
age: 47 outliers (0.5%)
fnlwgt: 297 outliers (3.0%)
educational-num: 360 outliers (3.6%)
capital-gain: 829 outliers (8.3%)
capital-loss: 426 outliers (4.3%)
hours-per-week: 2680 outliers (26.8%)

=== COMPARAISON CORRÉLATIONS PEARSON vs SPEARMAN ===

=== CORRÉLATIONS AVEC LA CIBLE ===
educational-num: 0.330
hours-per-week: 0.231
capital-gain: 0.229
age: 0.221
capital-loss: 0.136

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 6 variables
Justification: Médiane choisie car plus robuste aux outliers détectés
Imputation 'workclass': mode='Private' (5.5% manquant)
Imputation 'occupation': mode='Prof-specialty' (5.5% manquant)
Imputation 'native-country': mode='United-States' (1.9% manquant)
Justification: 'Unknown' si >20% manquant, sinon mode
One-Hot Encoding pour workclass: 7 nouvelles variables
Ordinal Encoding pour education: 16 niveaux ordonnés
One-Hot Encoding pour marital-status: 6 nouvelles variables
Label Encoding pour occupation
One-Hot Encoding pour relationship: 5 nouvelles variables
One-Hot Encoding pour race: 4 nouvelles variables
One-Hot Encoding pour gender: 1 nouvelles variables
Label Encoding pour native-country
MinMaxScaler appliqué à 32 variables
Justification: MinMaxScaler choisi car présence d'outliers significatifs
Split 70/30: Train=(7000, 32), Test=(3000, 32)
SMOTE appliqué: 10722 échantillons équilibrés

=== SUGGESTIONS DE FEATURE ENGINEERING ===
1. Transformation log pour 'fnlwgt' (asymétrie: 1.64)
2. Transformation log pour 'capital-gain' (asymétrie: 11.86)
3. Transformation log pour 'capital-loss' (asymétrie: 4.87)
4. Traitement outliers pour 'capital-gain' (8.3%)
5. Traitement outliers pour 'hours-per-week' (26.8%)
6. Regroupement de catégories pour 'native-country' (40 modalités)

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8146 | Acc: 0.8023 | Over: 0.0221
Decision Tree        | F1: 0.8074 | Acc: 0.8040 | Over: 0.1960
Random Forest        | F1: 0.8562 | Acc: 0.8563 | Over: 0.1437
KNN                  | F1: 0.7944 | Acc: 0.7827 | Over: 0.1171
SVM                  | F1: 0.7811 | Acc: 0.7643 | Over: 0.0563
Naive Bayes          | F1: 0.5854 | Acc: 0.5640 | Over: 0.1380
Gradient Boosting    | F1: 0.8493 | Acc: 0.8443 | Over: 0.0363

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Random Forest        | F1-CV: 0.8991 (±0.0057)
Gradient Boosting    | F1-CV: 0.8711 (±0.0076)
Logistic Regression  | F1-CV: 0.8228 (±0.0081)

--- Validation croisée 7-folds ---
Random Forest        | F1-CV: 0.9021 (±0.0062)
Gradient Boosting    | F1-CV: 0.8712 (±0.0066)
Logistic Regression  | F1-CV: 0.8234 (±0.0067)

--- Validation croisée 10-folds ---
Random Forest        | F1-CV: 0.9042 (±0.0104)
Gradient Boosting    | F1-CV: 0.8705 (±0.0123)
Logistic Regression  | F1-CV: 0.8225 (±0.0122)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Random Forest        0.8562   0.8563   0.9054   0.1437      
Gradient Boosting    0.8493   0.8443   0.9142   0.0363      
Logistic Regression  0.8146   0.8023   0.8958   0.0221      
Decision Tree        0.8074   0.8040   0.7457   0.1960      
KNN                  0.7944   0.7827   0.8355   0.1171      
SVM                  0.7811   0.7643   0.8940   0.0563      
Naive Bayes          0.5854   0.5640   0.8610   0.1380      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Random Forest
Performance F1: 0.8562

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Random Forest

              precision    recall  f1-score   support

           0       0.91      0.91      0.91      2297
           1       0.69      0.69      0.69       703

    accuracy                           0.86      3000
   macro avg       0.80      0.80      0.80      3000
weighted avg       0.86      0.86      0.86      3000


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.1014
⚠️ Overfitting détecté - Recommandations:
- Réduire la complexité des modèles
- Augmenter la régularisation
- Collecter plus de données

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Random Forest
   - Performance F1: 0.8562
   - Accuracy: 0.8563

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
