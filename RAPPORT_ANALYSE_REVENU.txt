RAPPORT D'ANALYSE CRISP-DM - REVENU
============================================================
Date: 2025-06-21 13:55:36

=== ANALYSE DU DATASET REVENU ===
Dimensions: (10000, 15)
Variable cible: income
Distribution cible:
income
<=50K    7658
>50K     2342
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
               age        fnlwgt  educational-num  capital-gain  capital-loss  hours-per-week
count  10000.00000  1.000000e+04      10000.00000  10000.000000  10000.000000    10000.000000
mean      38.72140  1.897136e+05         10.08070   1090.313900     80.158500       40.407600
std       13.65596  1.064721e+05          2.56668   7462.144975    388.835421       12.110125
min       17.00000  1.882700e+04          1.00000      0.000000      0.000000        1.000000
25%       28.00000  1.168238e+05          9.00000      0.000000      0.000000       40.000000
50%       37.00000  1.785715e+05         10.00000      0.000000      0.000000       40.000000
75%       48.00000  2.368182e+05         12.00000      0.000000      0.000000       45.000000
max       90.00000  1.490400e+06         16.00000  99999.000000   4356.000000       99.000000

=== VALEURS MANQUANTES ===
workclass         552
occupation        553
native-country    193
dtype: int64

Variables numériques: ['age', 'fnlwgt', 'educational-num', 'capital-gain', 'capital-loss', 'hours-per-week']
Variables catégorielles: ['workclass', 'education', 'marital-status', 'occupation', 'relationship', 'race', 'gender', 'native-country']

=== CORRÉLATIONS AVEC LA CIBLE ===
educational-num: 0.330
hours-per-week: 0.231
capital-gain: 0.229
age: 0.221
capital-loss: 0.136

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 6 variables
Imputation catégorielle (mode) pour 8 variables
One-Hot Encoding pour workclass: 7 nouvelles variables
Label Encoding pour education
One-Hot Encoding pour marital-status: 6 nouvelles variables
Label Encoding pour occupation
One-Hot Encoding pour relationship: 5 nouvelles variables
One-Hot Encoding pour race: 4 nouvelles variables
One-Hot Encoding pour gender: 1 nouvelles variables
Label Encoding pour native-country
StandardScaler appliqué à 32 variables
Split 70/30: Train=(7000, 32), Test=(3000, 32)
SMOTE appliqué: 10722 échantillons équilibrés

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8244 | Acc: 0.8137 | Over: 0.0150
Decision Tree        | F1: 0.7999 | Acc: 0.7970 | Over: 0.2030
Random Forest        | F1: 0.8551 | Acc: 0.8557 | Over: 0.1443
KNN                  | F1: 0.7967 | Acc: 0.7850 | Over: 0.1198
SVM                  | F1: 0.8134 | Acc: 0.8013 | Over: 0.0534
Naive Bayes          | F1: 0.5479 | Acc: 0.5303 | Over: 0.1486
Gradient Boosting    | F1: 0.8500 | Acc: 0.8453 | Over: 0.0379

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Random Forest        | F1-CV: 0.9031 (±0.0044)
Gradient Boosting    | F1-CV: 0.8745 (±0.0084)
Logistic Regression  | F1-CV: 0.8255 (±0.0074)

--- Validation croisée 7-folds ---
Random Forest        | F1-CV: 0.9030 (±0.0059)
Gradient Boosting    | F1-CV: 0.8751 (±0.0053)
Logistic Regression  | F1-CV: 0.8271 (±0.0063)

--- Validation croisée 10-folds ---
Random Forest        | F1-CV: 0.9063 (±0.0111)
Gradient Boosting    | F1-CV: 0.8738 (±0.0140)
Logistic Regression  | F1-CV: 0.8270 (±0.0138)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Random Forest        0.8551   0.8557   0.9066   0.1443      
Gradient Boosting    0.8500   0.8453   0.9158   0.0379      
Logistic Regression  0.8244   0.8137   0.9004   0.0150      
SVM                  0.8134   0.8013   0.8917   0.0534      
Decision Tree        0.7999   0.7970   0.7322   0.2030      
KNN                  0.7967   0.7850   0.8365   0.1198      
Naive Bayes          0.5479   0.5303   0.8586   0.1486      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Random Forest
Performance F1: 0.8551

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Random Forest

              precision    recall  f1-score   support

           0       0.90      0.91      0.91      2297
           1       0.70      0.68      0.69       703

    accuracy                           0.86      3000
   macro avg       0.80      0.80      0.80      3000
weighted avg       0.85      0.86      0.86      3000


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.1031
⚠️ Overfitting détecté - Recommandations:
- Réduire la complexité des modèles
- Augmenter la régularisation
- Collecter plus de données

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Random Forest
   - Performance F1: 0.8551
   - Accuracy: 0.8557

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
