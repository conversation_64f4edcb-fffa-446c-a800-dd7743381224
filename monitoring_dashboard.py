#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Dashboard de Monitoring - TP1 INF5082
Surveillance en temps réel des modèles en production
"""

import streamlit as st
import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time

# Configuration de la page
st.set_page_config(
    page_title="Dashboard Monitoring - TP1 INF5082",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

def load_performance_data():
    """Charge les données de performance"""
    performance_data = []
    
    # Charger tous les fichiers de performance
    if os.path.exists("monitoring"):
        for file in os.listdir("monitoring"):
            if file.startswith("performance_") and file.endswith(".json"):
                try:
                    with open(f"monitoring/{file}", 'r') as f:
                        data = json.load(f)
                        performance_data.extend(data)
                except:
                    continue
    
    return pd.DataFrame(performance_data)

def load_prediction_logs():
    """Charge les logs de prédiction"""
    prediction_logs = []
    
    # Charger tous les fichiers de logs
    if os.path.exists("logs"):
        for file in os.listdir("logs"):
            if file.startswith("predictions_") and file.endswith(".json"):
                try:
                    with open(f"logs/{file}", 'r') as f:
                        data = json.load(f)
                        prediction_logs.extend(data)
                except:
                    continue
    
    return pd.DataFrame(prediction_logs)

def load_alerts():
    """Charge les alertes récentes"""
    # Simuler des alertes pour la démo
    alerts = [
        {
            'timestamp': (datetime.now() - timedelta(hours=2)).isoformat(),
            'type': 'PERFORMANCE_DEGRADATION',
            'metric': 'accuracy',
            'value': 0.78,
            'threshold': 0.8,
            'severity': 'MEDIUM'
        },
        {
            'timestamp': (datetime.now() - timedelta(hours=6)).isoformat(),
            'type': 'HIGH_RESPONSE_TIME',
            'metric': 'response_time',
            'value': 2.5,
            'threshold': 2.0,
            'severity': 'LOW'
        }
    ]
    
    return alerts

def main():
    """Interface principale du dashboard"""
    
    # Titre principal
    st.title("📊 Dashboard de Monitoring")
    st.markdown("**TP1 INF5082 - Surveillance des Modèles en Production**")
    
    # Sidebar pour les contrôles
    st.sidebar.header("🔧 Contrôles")
    
    # Bouton de rafraîchissement
    if st.sidebar.button("🔄 Actualiser"):
        st.rerun()
    
    # Sélection de la période
    period = st.sidebar.selectbox(
        "📅 Période d'analyse",
        ["Dernières 24h", "Derniers 7 jours", "Dernier mois"]
    )
    
    # Auto-refresh
    auto_refresh = st.sidebar.checkbox("🔄 Actualisation automatique (30s)")
    
    if auto_refresh:
        time.sleep(30)
        st.rerun()
    
    # Métriques principales
    st.header("📈 Métriques Principales")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="🎯 Accuracy Actuelle",
            value="85.2%",
            delta="2.1%"
        )
    
    with col2:
        st.metric(
            label="⚡ Temps de Réponse Moyen",
            value="1.2s",
            delta="-0.3s"
        )
    
    with col3:
        st.metric(
            label="📊 Prédictions Aujourd'hui",
            value="1,247",
            delta="156"
        )
    
    with col4:
        st.metric(
            label="🚨 Alertes Actives",
            value="2",
            delta="1"
        )
    
    # Graphiques de performance
    st.header("📊 Évolution des Performances")
    
    # Charger les données
    perf_data = load_performance_data()
    
    if not perf_data.empty:
        # Convertir les timestamps
        perf_data['timestamp'] = pd.to_datetime(perf_data['timestamp'])
        
        # Graphique des métriques dans le temps
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Accuracy', 'F1-Score', 'Precision', 'Recall'),
            vertical_spacing=0.1
        )
        
        metrics = ['accuracy', 'f1', 'precision', 'recall']
        positions = [(1,1), (1,2), (2,1), (2,2)]
        
        for metric, (row, col) in zip(metrics, positions):
            if metric in perf_data.columns:
                fig.add_trace(
                    go.Scatter(
                        x=perf_data['timestamp'],
                        y=perf_data[metric],
                        mode='lines+markers',
                        name=metric.capitalize(),
                        line=dict(width=2)
                    ),
                    row=row, col=col
                )
        
        fig.update_layout(height=500, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("Aucune donnée de performance disponible. Lancez des prédictions pour voir les métriques.")
    
    # Logs de prédiction
    st.header("📝 Activité des Prédictions")
    
    pred_logs = load_prediction_logs()
    
    if not pred_logs.empty:
        # Convertir les timestamps
        pred_logs['timestamp'] = pd.to_datetime(pred_logs['timestamp'])
        pred_logs['hour'] = pred_logs['timestamp'].dt.hour
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Graphique du nombre de prédictions par heure
            hourly_preds = pred_logs.groupby('hour')['n_samples'].sum().reset_index()
            
            fig = px.bar(
                hourly_preds,
                x='hour',
                y='n_samples',
                title="Prédictions par Heure",
                labels={'hour': 'Heure', 'n_samples': 'Nombre de Prédictions'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Graphique des temps de réponse
            fig = px.histogram(
                pred_logs,
                x='response_time',
                title="Distribution des Temps de Réponse",
                labels={'response_time': 'Temps de Réponse (s)', 'count': 'Fréquence'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Tableau des dernières prédictions
        st.subheader("🕐 Dernières Prédictions")
        recent_preds = pred_logs.sort_values('timestamp', ascending=False).head(10)
        st.dataframe(
            recent_preds[['timestamp', 'n_samples', 'response_time', 'model_version']],
            use_container_width=True
        )
    else:
        st.info("Aucun log de prédiction disponible.")
    
    # Alertes
    st.header("🚨 Alertes et Notifications")
    
    alerts = load_alerts()
    
    if alerts:
        for alert in alerts:
            severity_color = {
                'LOW': '🟡',
                'MEDIUM': '🟠', 
                'HIGH': '🔴',
                'CRITICAL': '🚨'
            }
            
            st.warning(
                f"{severity_color.get(alert['severity'], '⚠️')} "
                f"**{alert['type']}** - "
                f"{alert['metric']}: {alert['value']} "
                f"(seuil: {alert['threshold']}) - "
                f"{alert['timestamp']}"
            )
    else:
        st.success("✅ Aucune alerte active")
    
    # Configuration et statut du système
    st.header("⚙️ Configuration et Statut")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📋 Configuration Actuelle")
        
        config_data = {
            "Modèle": "Random Forest",
            "Version": "1.0_20241221",
            "Dataset": "Crédit",
            "Seuil d'alerte Accuracy": "80%",
            "Seuil d'alerte F1": "75%",
            "Intervalle de monitoring": "5 minutes"
        }
        
        for key, value in config_data.items():
            st.text(f"{key}: {value}")
    
    with col2:
        st.subheader("🔍 Statut du Système")
        
        status_data = {
            "API": "🟢 Opérationnelle",
            "Base de données": "🟢 Connectée",
            "Monitoring": "🟢 Actif",
            "Sauvegarde": "🟢 OK",
            "Dernière vérification": datetime.now().strftime("%H:%M:%S")
        }
        
        for key, value in status_data.items():
            st.text(f"{key}: {value}")
    
    # Actions rapides
    st.header("⚡ Actions Rapides")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 Redémarrer API"):
            st.info("Redémarrage de l'API en cours...")
    
    with col2:
        if st.button("💾 Sauvegarder Modèle"):
            st.info("Sauvegarde du modèle en cours...")
    
    with col3:
        if st.button("📊 Générer Rapport"):
            st.info("Génération du rapport en cours...")
    
    with col4:
        if st.button("🔔 Test Alerte"):
            st.warning("Alerte de test envoyée!")
    
    # Footer
    st.markdown("---")
    st.markdown(
        "**Dashboard de Monitoring TP1 INF5082** | "
        f"Dernière mise à jour: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

if __name__ == "__main__":
    main()
