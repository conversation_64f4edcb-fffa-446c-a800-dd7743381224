import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB

# Techniques d'equilibrage avancees
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE, RandomOverSampler
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTEENN, SMOTETomek

# Imports pour EDA avance
from sklearn.preprocessing import MinMaxScaler, OrdinalEncoder
from scipy import stats

import warnings
import sys
import os
import glob

# Configuration des warnings
warnings.filterwarnings('ignore')
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', message='.*ConvergenceWarning.*')
warnings.filterwarnings('ignore', message='.*Solver terminated early.*')

# Configuration pour sklearn
import os
os.environ['PYTHONWARNINGS'] = 'ignore'

# Configuration pour les visualisations
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("Set2")

# ============================================================================
# FONCTIONS EDA AVANCEES FUSIONNEES
# ============================================================================

def nettoyer_valeurs_manquantes(df):
    """
    Nettoie les valeurs manquantes incluant les '?' → np.nan
    """
    print("=== NETTOYAGE DES VALEURS MANQUANTES ===")

    # Remplacement des '?' par np.nan
    df_clean = df.replace('?', np.nan)
    df_clean = df_clean.replace(' ?', np.nan)  # Avec espaces
    df_clean = df_clean.replace('? ', np.nan)  # Avec espaces

    print("Valeurs '?' remplacees par np.nan")

    # Affichage des valeurs manquantes
    valeurs_manquantes = df_clean.isnull().sum()
    pourcentage_manquant = (valeurs_manquantes / len(df_clean)) * 100

    print("\nValeurs manquantes par colonne:")
    print("-" * 50)
    for col in df_clean.columns:
        if valeurs_manquantes[col] > 0:
            print(f"{col:<20} : {valeurs_manquantes[col]:>6} ({pourcentage_manquant[col]:>5.1f}%)")

    return df_clean

def statistiques_detaillees(df):
    """
    Statistiques de position et dispersion detaillees
    """
    print(f"\n=== STATISTIQUES DETAILLEES ===")

    variables_numeriques = df.select_dtypes(include=[np.number]).columns

    if len(variables_numeriques) > 0:
        stats_df = pd.DataFrame(index=variables_numeriques)

        for col in variables_numeriques:
            data = df[col].dropna()
            stats_df.loc[col, 'Moyenne'] = data.mean()
            stats_df.loc[col, 'Mediane'] = data.median()
            stats_df.loc[col, 'Ecart-type'] = data.std()
            stats_df.loc[col, 'IQR'] = data.quantile(0.75) - data.quantile(0.25)
            stats_df.loc[col, 'Q1'] = data.quantile(0.25)
            stats_df.loc[col, 'Q3'] = data.quantile(0.75)
            stats_df.loc[col, 'Min'] = data.min()
            stats_df.loc[col, 'Max'] = data.max()
            stats_df.loc[col, 'Asymetrie'] = data.skew()
            stats_df.loc[col, 'Aplatissement'] = data.kurtosis()

        print("\nStatistiques de position et dispersion:")
        print(stats_df.round(3))

        return stats_df
    else:
        print("Aucune variable numerique trouvee")
        return None

def detecter_outliers(df, methode='iqr'):
    """
    Detection des outliers avec recommandations
    """
    print(f"\n=== DETECTION DES OUTLIERS (methode: {methode.upper()}) ===")

    variables_numeriques = df.select_dtypes(include=[np.number]).columns
    outliers_info = {}

    for col in variables_numeriques:
        data = df[col].dropna()

        if methode == 'iqr':
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            limite_inf = Q1 - 1.5 * IQR
            limite_sup = Q3 + 1.5 * IQR
            outliers = data[(data < limite_inf) | (data > limite_sup)]

        elif methode == 'zscore':
            z_scores = np.abs(stats.zscore(data))
            outliers = data[z_scores > 3]

        else:
            print(f"Methode '{methode}' non reconnue")
            continue

        pourcentage = (len(outliers) / len(data)) * 100
        outliers_info[col] = {
            'nombre': len(outliers),
            'pourcentage': pourcentage,
            'valeurs': outliers.tolist()[:10]  # Max 10 valeurs
        }

        print(f"{col:<20} : {len(outliers):>4} outliers ({pourcentage:>5.1f}%)")

    # Recommandations
    print(f"\n=== RECOMMANDATIONS POUR LE TRAITEMENT DES OUTLIERS ===")
    for col, info in outliers_info.items():
        if info['pourcentage'] > 5:
            print(f"{col}: ATTENTION - {info['pourcentage']:.1f}% d'outliers")
            print(f"  → Recommandation: Transformation log ou suppression")
        elif info['pourcentage'] > 1:
            print(f"{col}: {info['pourcentage']:.1f}% d'outliers")
            print(f"  → Recommandation: Winsorisation ou cap/floor")
        elif info['nombre'] > 0:
            print(f"{col}: Quelques outliers ({info['nombre']})")
            print(f"  → Recommandation: Conserver ou investigation manuelle")

    return outliers_info

def correlation_avancee(df, col_cible, type_donnees):
    """
    Analyse de correlation avancee (Pearson + Spearman)
    """
    print(f"\n=== ANALYSE DE CORRELATION AVANCEE ===")

    variables_numeriques = df.select_dtypes(include=[np.number]).columns

    if len(variables_numeriques) > 1:
        # Correlation de Pearson
        corr_pearson = df[variables_numeriques].corr()

        # Correlation de Spearman
        corr_spearman = df[variables_numeriques].corr(method='spearman')

        # Visualisation
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))

        # Heatmap Pearson
        sns.heatmap(corr_pearson, annot=True, cmap='coolwarm', center=0,
                   linewidths=0.5, ax=axes[0])
        axes[0].set_title('Matrice de Correlation - Pearson')

        # Heatmap Spearman
        sns.heatmap(corr_spearman, annot=True, cmap='coolwarm', center=0,
                   linewidths=0.5, ax=axes[1])
        axes[1].set_title('Matrice de Correlation - Spearman')

        plt.tight_layout()
        plt.savefig(f'{type_donnees}_correlation_avancee.png', dpi=300, bbox_inches='tight')
        plt.close()

        # Variables les plus correlees avec la cible
        if col_cible in variables_numeriques:
            print(f"\nVariables les plus correlees avec '{col_cible}' (Pearson):")
            corr_cible = corr_pearson[col_cible].abs().sort_values(ascending=False)
            for var, corr in corr_cible.head(10).items():
                if var != col_cible:
                    print(f"  {var:<20} : {corr:>6.3f}")

        return corr_pearson, corr_spearman
    else:
        print("Pas assez de variables numeriques pour l'analyse de correlation")
        return None, None

def suggestions_feature_engineering(df, col_cible, outliers_info):
    """
    Suggestions de feature engineering
    """
    print(f"\n=== SUGGESTIONS DE FEATURE ENGINEERING ===")

    suggestions = []

    # Variables numeriques
    variables_numeriques = df.select_dtypes(include=[np.number]).columns

    for col in variables_numeriques:
        if col != col_cible:
            data = df[col].dropna()

            # Transformation log si asymetrie elevee
            if abs(data.skew()) > 1:
                suggestions.append(f"Transformation log pour '{col}' (asymetrie: {data.skew():.2f})")

            # Binning si beaucoup de valeurs uniques
            if data.nunique() > 50:
                suggestions.append(f"Binning pour '{col}' ({data.nunique()} valeurs uniques)")

            # Traitement outliers
            if col in outliers_info and outliers_info[col]['pourcentage'] > 5:
                suggestions.append(f"Traitement outliers pour '{col}' ({outliers_info[col]['pourcentage']:.1f}%)")

    # Variables categorielles
    variables_categorielles = df.select_dtypes(include=['object']).columns

    for col in variables_categorielles:
        if col != col_cible:
            # Regroupement si trop de categories
            if df[col].nunique() > 20:
                suggestions.append(f"Regroupement de categories pour '{col}' ({df[col].nunique()} categories)")

            # Encodage de frequence
            if df[col].nunique() > 10:
                suggestions.append(f"Encodage de frequence pour '{col}'")

    # Interactions possibles
    if len(variables_numeriques) > 2:
        suggestions.append("Creation d'interactions entre variables numeriques")

    # Affichage des suggestions
    print("Suggestions identifiees:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i:2d}. {suggestion}")

    if not suggestions:
        print("Aucune suggestion specifique identifiee")

    return suggestions

def encoder_avec_label_encoder(df, colonnes_categorielles):
    """
    Alternative d'encodage utilisant LabelEncoder pour les variables catégorielles
    """
    print(f"\n=== ENCODAGE AVEC LABEL ENCODER ===")

    df_encoded = df.copy()
    label_encoders = {}

    for col in colonnes_categorielles:
        if col in df_encoded.columns:
            print(f"Encodage de la colonne '{col}'...")

            # Créer et ajuster le LabelEncoder
            le = LabelEncoder()

            # Gérer les valeurs manquantes
            df_encoded[col] = df_encoded[col].fillna('Unknown')

            # Encoder la colonne
            df_encoded[col + '_encoded'] = le.fit_transform(df_encoded[col].astype(str))

            # Sauvegarder l'encoder pour usage futur
            label_encoders[col] = le

            # Afficher le mapping
            classes = le.classes_
            print(f"  Mapping pour '{col}': {dict(zip(classes, range(len(classes))))}")

    print(f"Encodage terminé pour {len(colonnes_categorielles)} colonnes")
    return df_encoded, label_encoders

def validation_croisee_avec_equilibrage(X, y, modele, technique_equilibrage, folds=[5, 7, 10]):
    """
    Validation croisee avec equilibrage applique sur chaque fold
    """
    print(f"\n=== VALIDATION CROISEE AVEC EQUILIBRAGE ===")

    resultats_cv = {}

    for n_folds in folds:
        print(f"\nValidation croisee {n_folds}-folds:")

        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        scores_f1 = []
        scores_accuracy = []
        scores_precision = []
        scores_recall = []

        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
            X_train_fold = X.iloc[train_idx]
            y_train_fold = y.iloc[train_idx]
            X_val_fold = X.iloc[val_idx]
            y_val_fold = y.iloc[val_idx]

            # Application de la technique d'equilibrage sur le fold d'entrainement
            try:
                if technique_equilibrage is None:
                    X_train_eq, y_train_eq = X_train_fold, y_train_fold
                else:
                    X_train_eq, y_train_eq = technique_equilibrage.fit_resample(X_train_fold, y_train_fold)

                # Entrainement du modele
                modele_fold = modele.__class__(**modele.get_params())
                modele_fold.fit(X_train_eq, y_train_eq)

                # Prediction sur le fold de validation
                y_pred = modele_fold.predict(X_val_fold)

                # Calcul des metriques
                f1 = f1_score(y_val_fold, y_pred, average='weighted')
                accuracy = accuracy_score(y_val_fold, y_pred)
                precision = precision_score(y_val_fold, y_pred, average='weighted')
                recall = recall_score(y_val_fold, y_pred, average='weighted')

                scores_f1.append(f1)
                scores_accuracy.append(accuracy)
                scores_precision.append(precision)
                scores_recall.append(recall)

            except Exception as e:
                print(f"  Erreur fold {fold+1}: {str(e)[:50]}...")
                continue

        if scores_f1:
            # Calcul des statistiques
            resultats_cv[f'{n_folds}_folds'] = {
                'f1_mean': np.mean(scores_f1),
                'f1_std': np.std(scores_f1),
                'accuracy_mean': np.mean(scores_accuracy),
                'accuracy_std': np.std(scores_accuracy),
                'precision_mean': np.mean(scores_precision),
                'precision_std': np.std(scores_precision),
                'recall_mean': np.mean(scores_recall),
                'recall_std': np.std(scores_recall),
                'variance_f1': np.var(scores_f1)
            }

            print(f"  F1-Score: {np.mean(scores_f1):.4f} (±{np.std(scores_f1):.4f})")
            print(f"  Accuracy: {np.mean(scores_accuracy):.4f} (±{np.std(scores_accuracy):.4f})")
            print(f"  Variance F1: {np.var(scores_f1):.6f}")

    return resultats_cv

def tableau_comparatif_complet(resultats_complets, resultats_cv_dict=None):
    """
    Cree un tableau comparatif complet avec validation croisee
    """
    print(f"\n=== TABLEAU COMPARATIF COMPLET ===")

    # Preparation des donnees
    donnees_tableau = []

    for technique, modeles in resultats_complets.items():
        for modele, metriques in modeles.items():
            if metriques is not None:
                ligne = {
                    'Technique': technique,
                    'Modele': modele,
                    'F1_Test': metriques['f1_score'],
                    'Accuracy_Test': metriques['test_accuracy'],
                    'Precision_Test': metriques['precision'],
                    'Recall_Test': metriques['recall'],
                    'ROC_AUC': metriques['roc_auc'] if metriques['roc_auc'] else 0,
                    'Overfitting': metriques['overfitting']
                }

                # Ajout des resultats de validation croisee si disponibles
                if resultats_cv_dict and f"{technique}_{modele}" in resultats_cv_dict:
                    cv_results = resultats_cv_dict[f"{technique}_{modele}"]
                    ligne.update({
                        'F1_CV_5': cv_results.get('5_folds', {}).get('f1_mean', 0),
                        'F1_CV_7': cv_results.get('7_folds', {}).get('f1_mean', 0),
                        'F1_CV_10': cv_results.get('10_folds', {}).get('f1_mean', 0),
                        'Variance_CV': cv_results.get('10_folds', {}).get('variance_f1', 0)
                    })

                donnees_tableau.append(ligne)

    df_tableau = pd.DataFrame(donnees_tableau)

    if not df_tableau.empty:
        # Tri par F1-Score
        df_tableau = df_tableau.sort_values('F1_Test', ascending=False)

        print("\nTableau comparatif complet (Top 15):")
        print("=" * 120)

        # Affichage avec formatage
        colonnes_affichage = ['Technique', 'Modele', 'F1_Test', 'Accuracy_Test', 'ROC_AUC', 'Overfitting']
        if 'F1_CV_10' in df_tableau.columns:
            colonnes_affichage.extend(['F1_CV_10', 'Variance_CV'])

        for i, (_, row) in enumerate(df_tableau.head(15).iterrows()):
            if i == 0:
                # En-tete
                print(f"{'Rang':<4} {'Technique':<20} {'Modele':<25} {'F1':<8} {'Acc':<8} {'AUC':<8} {'Over':<8}", end="")
                if 'F1_CV_10' in df_tableau.columns:
                    print(f" {'CV10':<8} {'Var':<8}")
                else:
                    print()
                print("-" * 120)

            print(f"{i+1:<4} {row['Technique']:<20} {row['Modele']:<25} "
                  f"{row['F1_Test']:<8.4f} {row['Accuracy_Test']:<8.4f} "
                  f"{row['ROC_AUC']:<8.4f} {row['Overfitting']:<8.4f}", end="")

            if 'F1_CV_10' in df_tableau.columns:
                print(f" {row['F1_CV_10']:<8.4f} {row['Variance_CV']:<8.6f}")
            else:
                print()

        return df_tableau
    else:
        print("Aucune donnee pour le tableau comparatif")
        return None

def recommandations_avancees(df_resultats, resultats_eda, type_donnees):
    """
    Genere des recommandations avancees pour l'amelioration des modeles
    """
    print(f"\n=== RECOMMANDATIONS AVANCEES ===")

    if df_resultats is None or df_resultats.empty:
        print("Aucune donnee pour les recommandations")
        return []

    recommandations = []

    # 1. Analyse des performances par classe minoritaire
    print("\n1. ANALYSE DE LA CLASSE MINORITAIRE:")
    meilleur_modele = df_resultats.iloc[0]

    if meilleur_modele['F1-Score'] < 0.7:
        recommandations.append("Performance faible sur classe minoritaire - Augmenter l'equilibrage")
        print("  → Performance faible detectee (F1 < 0.7)")
        print("  → Recommandation: Techniques d'equilibrage plus agressives")
    elif meilleur_modele['F1-Score'] < 0.8:
        recommandations.append("Performance moderee - Optimiser l'equilibrage et les hyperparametres")
        print("  → Performance moderee detectee (0.7 ≤ F1 < 0.8)")
        print("  → Recommandation: Fine-tuning des hyperparametres")
    else:
        recommandations.append("Excellente performance - Maintenir l'approche actuelle")
        print("  → Excellente performance detectee (F1 ≥ 0.8)")
        print("  → Recommandation: Validation sur donnees externes")

    # 2. Analyse de l'overfitting
    print("\n2. ANALYSE DE L'OVERFITTING:")
    overfitting_moyen = df_resultats['Overfitting'].mean()

    if overfitting_moyen > 0.1:
        recommandations.append("Overfitting detecte - Regularisation necessaire")
        print(f"  → Overfitting eleve detecte (moyenne: {overfitting_moyen:.3f})")
        print("  → Recommandations:")
        print("    - Augmenter la regularisation (C plus faible pour SVM/LogReg)")
        print("    - Reduire max_depth pour arbres")
        print("    - Augmenter min_samples_split pour Random Forest")
        print("    - Utiliser Dropout pour reseaux de neurones")
    elif overfitting_moyen > 0.05:
        recommandations.append("Overfitting modere - Surveillance recommandee")
        print(f"  → Overfitting modere detecte (moyenne: {overfitting_moyen:.3f})")
        print("  → Recommandation: Surveillance et validation croisee")
    else:
        recommandations.append("Pas d'overfitting significatif detecte")
        print(f"  → Overfitting faible (moyenne: {overfitting_moyen:.3f})")
        print("  → Recommandation: Modeles bien generalises")

    # 3. Feature Engineering base sur l'EDA
    print("\n3. FEATURE ENGINEERING RECOMMANDE:")
    if resultats_eda and resultats_eda['suggestions']:
        print("  Suggestions basees sur l'EDA:")
        for i, suggestion in enumerate(resultats_eda['suggestions'][:5], 1):
            print(f"    {i}. {suggestion}")
            recommandations.append(f"Feature engineering: {suggestion}")

    # Suggestions specifiques selon les outliers
    if resultats_eda and resultats_eda['outliers_info']:
        outliers_significatifs = {k: v for k, v in resultats_eda['outliers_info'].items()
                                 if v['pourcentage'] > 5}
        if outliers_significatifs:
            print("  Traitement des outliers recommande:")
            for var, info in outliers_significatifs.items():
                print(f"    - {var}: {info['pourcentage']:.1f}% outliers → Winsorisation ou transformation")
                recommandations.append(f"Traiter outliers dans {var} ({info['pourcentage']:.1f}%)")

    # 4. Hyperparametrage specifique par algorithme
    print("\n4. HYPERPARAMETRAGE RECOMMANDE:")

    # Analyse des meilleurs modeles par type
    modeles_par_type = {}
    for _, row in df_resultats.head(10).iterrows():
        modele_type = row['Modele'].split()[0]  # Premier mot du nom
        if modele_type not in modeles_par_type:
            modeles_par_type[modele_type] = row

    for modele_type, meilleur in modeles_par_type.items():
        print(f"  {modele_type}:")

        if 'Random' in modele_type:
            print("    - n_estimators: [100, 200, 500]")
            print("    - max_depth: [10, 20, None]")
            print("    - min_samples_split: [2, 5, 10]")
            recommandations.append(f"Optimiser Random Forest: n_estimators, max_depth, min_samples_split")

        elif 'SVM' in modele_type:
            print("    - C: [0.1, 1, 10, 100]")
            print("    - gamma: ['scale', 'auto', 0.001, 0.01]")
            print("    - kernel: ['rbf', 'poly'] si lineaire insuffisant")
            recommandations.append(f"Optimiser SVM: C, gamma, kernel")

        elif 'Regression' in modele_type:
            print("    - C: [0.01, 0.1, 1, 10]")
            print("    - solver: ['liblinear', 'lbfgs', 'saga']")
            print("    - penalty: ['l1', 'l2', 'elasticnet']")
            recommandations.append(f"Optimiser Regression Logistique: C, solver, penalty")

        elif 'Gradient' in modele_type:
            print("    - n_estimators: [100, 200, 500]")
            print("    - learning_rate: [0.01, 0.1, 0.2]")
            print("    - max_depth: [3, 5, 7]")
            recommandations.append(f"Optimiser Gradient Boosting: n_estimators, learning_rate, max_depth")

    # 5. Recommandations contextuelles
    print("\n5. RECOMMANDATIONS CONTEXTUELLES:")

    if type_donnees == 'credit':
        print("  Contexte financier (Credit):")
        print("    - Privilegier la precision pour reduire les faux positifs")
        print("    - Surveiller le recall pour ne pas manquer les vrais positifs")
        print("    - Considerer le cout metier des erreurs de classification")
        recommandations.append("Contexte credit: Equilibrer precision/recall selon cout metier")

    elif type_donnees == 'revenu':
        print("  Contexte socio-economique (Revenu):")
        print("    - F1-Score equilibre recommande")
        print("    - Attention aux biais demographiques")
        print("    - Validation sur differents groupes demographiques")
        recommandations.append("Contexte revenu: Valider absence de biais demographiques")

    # 6. Prochaines etapes
    print("\n6. PROCHAINES ETAPES PRIORITAIRES:")
    etapes = [
        "1. Validation croisee avec equilibrage sur chaque fold",
        "2. Optimisation des hyperparametres (GridSearch/RandomSearch)",
        "3. Feature engineering base sur les suggestions EDA",
        "4. Validation sur donnees externes",
        "5. Analyse de l'interpretabilite des modeles",
        "6. Mise en place du monitoring en production"
    ]

    for etape in etapes:
        print(f"  {etape}")
        recommandations.append(f"Prochaine etape: {etape}")

    return recommandations

def nettoyer_resultats_precedents():
    """Supprime les anciens fichiers de resultats"""
    print("Nettoyage des anciens fichiers de resultats...")
    patterns_fichiers = ["*.png", "*_analysis_summary.txt", "*_equilibrage_*.txt"]
    fichiers_supprimes = 0
    for pattern in patterns_fichiers:
        fichiers_a_supprimer = glob.glob(pattern)
        for chemin_fichier in fichiers_a_supprimer:
            try:
                os.remove(chemin_fichier)
                fichiers_supprimes += 1
                print(f"  Supprime: {chemin_fichier}")
            except Exception as e:
                print(f"  Impossible de supprimer {chemin_fichier}: {e}")
    if fichiers_supprimes > 0:
        print(f"{fichiers_supprimes} fichier(s) supprime(s)")
    else:
        print("Aucun ancien fichier trouve")
    print()

def selectionner_fichier_donnees():
    """Permet de selectionner le fichier de donnees a analyser"""
    if len(sys.argv) > 1:
        choix = sys.argv[1].strip().lower()
        print(f"Argument detecte: {choix}")
    else:
        print("=== SELECTION DU FICHIER DE DONNEES ===")
        print("Fichiers disponibles :")
        print("1. credit - Analyse des donnees de credit")
        print("2. revenu - Analyse des donnees de revenu")
        choix = input("\nEntrez votre choix (credit/revenu) : ").strip().lower()
    
    if choix in ['credit', '1']:
        chemin_fichier = './credit.csv'
        type_donnees = 'credit'
    elif choix in ['revenu', '2']:
        chemin_fichier = './revenu.csv'
        type_donnees = 'revenu'
    else:
        print("Choix invalide. Veuillez entrer 'credit' ou 'revenu'.")
        if len(sys.argv) > 1:
            sys.exit(1)
        else:
            return selectionner_fichier_donnees()
    
    try:
        if not os.path.exists(chemin_fichier):
            raise FileNotFoundError(f"Le fichier '{chemin_fichier}' n'existe pas.")
        donnees_test = pd.read_csv(chemin_fichier)
        print(f"Fichier '{chemin_fichier}' charge avec succes!")
        print(f"Dimensions du dataset: {donnees_test.shape}")
        return chemin_fichier, type_donnees
    except FileNotFoundError:
        print(f"Erreur: Le fichier '{chemin_fichier}' n'existe pas.")
        if len(sys.argv) > 1:
            sys.exit(1)
        else:
            return selectionner_fichier_donnees()

def detecter_variable_cible(df, type_donnees):
    """Detecte automatiquement la variable cible selon le type de donnees"""
    if type_donnees == 'credit':
        cibles_possibles = ['Approved', 'approved', 'Approval', 'approval', 'Target', 'target']
        for col in cibles_possibles:
            if col in df.columns:
                return col
    elif type_donnees == 'revenu':
        cibles_possibles = ['income', 'Income', 'Salary', 'salary', 'Revenue', 'revenue', 'Target', 'target']
        for col in cibles_possibles:
            if col in df.columns:
                return col
    
    if len(sys.argv) > 1:
        colonne_cible = df.columns[-1]
        print(f"Mode automatique: utilisation de la derniere colonne '{colonne_cible}' comme variable cible")
        return colonne_cible
    
    print("\nVariable cible non detectee automatiquement.")
    print("Colonnes disponibles:", list(df.columns))
    while True:
        colonne_cible = input("Veuillez entrer le nom de la variable cible : ").strip()
        if colonne_cible in df.columns:
            return colonne_cible
        else:
            print(f"Colonne '{colonne_cible}' non trouvee. Reessayez.")

def diagnostiquer_desequilibre(y, nom_dataset="Dataset"):
    """Diagnostic complet du desequilibre des classes"""
    print(f"\n=== DIAGNOSTIC DU DESEQUILIBRE - {nom_dataset.upper()} ===")
    
    distribution = pd.Series(y).value_counts().sort_index()
    print(f"Distribution des classes:")
    for classe, count in distribution.items():
        pourcentage = (count / len(y)) * 100
        print(f"  Classe {classe}: {count:,} echantillons ({pourcentage:.1f}%)")
    
    if len(distribution) == 2:
        classe_majoritaire = distribution.max()
        classe_minoritaire = distribution.min()
        ratio = classe_majoritaire / classe_minoritaire
        print(f"\nRatio de desequilibre: {ratio:.2f}:1")
        
        if ratio < 1.5:
            niveau = "EQUILIBRE"
        elif ratio < 3:
            niveau = "LEGER DESEQUILIBRE"
        elif ratio < 10:
            niveau = "DESEQUILIBRE MODERE"
        else:
            niveau = "DESEQUILIBRE SEVERE"
        
        print(f"Niveau de desequilibre: {niveau}")
        return ratio, niveau, distribution
    else:
        print(f"Dataset multi-classe avec {len(distribution)} classes")
        return None, "MULTI-CLASSE", distribution

def preprocesser_donnees_avance(df, col_cible, type_donnees,
                               strategie_categorielle='most_frequent',
                               methode_normalisation='standard',
                               variables_ordinales=None):
    """
    Preprocessing avance avec EDA complet integre
    """
    print(f"\n{'='*80}")
    print("PREPROCESSING AVANCE AVEC EDA COMPLET")
    print(f"{'='*80}")

    # 1. Nettoyage des valeurs manquantes (incluant '?' → np.nan)
    df_clean = nettoyer_valeurs_manquantes(df)

    # 2. Statistiques detaillees
    stats_detaillees = statistiques_detaillees(df_clean)

    # 3. Detection des outliers
    outliers_info = detecter_outliers(df_clean)

    # 4. Correlation avancee
    corr_pearson, corr_spearman = correlation_avancee(df_clean, col_cible, type_donnees)

    # 5. Suggestions de feature engineering
    suggestions = suggestions_feature_engineering(df_clean, col_cible, outliers_info)

    # 6. Preprocessing proprement dit
    print(f"\n{'='*60}")
    print("PREPROCESSING DES DONNEES")
    print(f"{'='*60}")

    df_traite = df_clean.copy()

    # Identification des variables
    variables_numeriques = []
    variables_categorielles = []

    for col in df_traite.columns:
        if col != col_cible:
            if df_traite[col].dtype in ['int64', 'float64'] and df_traite[col].nunique() > 10:
                variables_numeriques.append(col)
            elif df_traite[col].dtype == 'object' or df_traite[col].nunique() <= 10:
                variables_categorielles.append(col)

    print(f"Variables numeriques detectees: {variables_numeriques}")
    print(f"Variables categorielles detectees: {variables_categorielles}")

    # Imputation avancee
    if variables_numeriques:
        print(f"Imputation numerique (mediane) pour {len(variables_numeriques)} variables")
        imputeur_numerique = SimpleImputer(strategy='median')
        df_traite[variables_numeriques] = imputeur_numerique.fit_transform(df_traite[variables_numeriques])

    if variables_categorielles:
        if strategie_categorielle == 'unknown':
            print(f"Imputation categorielle (Unknown) pour {len(variables_categorielles)} variables")
            for col in variables_categorielles:
                df_traite[col] = df_traite[col].fillna('Unknown')
        else:
            print(f"Imputation categorielle (most_frequent) pour {len(variables_categorielles)} variables")
            imputeur_categoriel = SimpleImputer(strategy='most_frequent')
            df_traite[variables_categorielles] = imputeur_categoriel.fit_transform(df_traite[variables_categorielles])

    # Encodage avance avec support ordinal
    if variables_ordinales:
        print(f"Encodage ordinal pour: {list(variables_ordinales.keys())}")
        for col, ordre in variables_ordinales.items():
            if col in df_traite.columns and col in variables_categorielles:
                try:
                    encoder = OrdinalEncoder(categories=[ordre])
                    df_traite[col] = encoder.fit_transform(df_traite[[col]])
                    variables_categorielles.remove(col)
                    print(f"  {col}: {ordre}")
                except Exception as e:
                    print(f"  Erreur encodage ordinal pour {col}: {e}")

    # Encodage des variables categorielles restantes
    for variable in variables_categorielles:
        try:
            if df_traite[variable].nunique() == 2:
                valeurs_uniques = df_traite[variable].unique()
                df_traite[variable] = df_traite[variable].map({valeurs_uniques[0]: 0, valeurs_uniques[1]: 1})
                print(f"  Encodage binaire pour {variable}: {valeurs_uniques[0]}=0, {valeurs_uniques[1]}=1")
            else:
                # One-Hot Encoding pour variables avec plus de 2 categories
                variables_encodees = pd.get_dummies(df_traite[variable], prefix=variable, drop_first=True)
                df_traite = pd.concat([df_traite, variables_encodees], axis=1)
                df_traite.drop(variable, axis=1, inplace=True)
                print(f"  One-Hot Encoding pour {variable}: {len(variables_encodees.columns)} nouvelles variables")
        except Exception as e:
            print(f"Erreur lors de l'encodage de {variable}: {e}")
            try:
                df_traite[variable] = pd.Categorical(df_traite[variable]).codes
                print(f"  Encodage de secours pour {variable}")
            except:
                print(f"  Impossible d'encoder {variable}, suppression de la colonne")
                df_traite.drop(variable, axis=1, inplace=True)

    # Normalisation avancee avec choix de methode
    variables_numeriques_finales = [col for col in variables_numeriques if col in df_traite.columns]
    if variables_numeriques_finales:
        if methode_normalisation == 'standard':
            scaler = StandardScaler()
            print(f"StandardScaler applique a {len(variables_numeriques_finales)} variables")
        elif methode_normalisation == 'minmax':
            scaler = MinMaxScaler()
            print(f"MinMaxScaler applique a {len(variables_numeriques_finales)} variables")
        else:
            print(f"Methode '{methode_normalisation}' non reconnue, utilisation de StandardScaler")
            scaler = StandardScaler()

        df_traite[variables_numeriques_finales] = scaler.fit_transform(df_traite[variables_numeriques_finales])

    # Encodage de la variable cible si necessaire
    if df_traite[col_cible].dtype == 'object':
        valeurs_uniques = df_traite[col_cible].unique()
        if len(valeurs_uniques) == 2:
            df_traite[col_cible] = df_traite[col_cible].map({valeurs_uniques[0]: 0, valeurs_uniques[1]: 1})
            print(f"  Encodage de la variable cible {col_cible}: {valeurs_uniques[0]}=0, {valeurs_uniques[1]}=1")
        else:
            df_traite[col_cible] = pd.Categorical(df_traite[col_cible]).codes
            print(f"  Encodage multi-classe de la variable cible {col_cible}")

    # Separation des features et de la cible
    X = df_traite.drop(col_cible, axis=1)
    y = df_traite[col_cible]

    print(f"\nPreprocessing termine!")
    print(f"Dimensions finales: {X.shape}")

    # Retour des resultats EDA pour reference
    resultats_eda = {
        'stats_detaillees': stats_detaillees,
        'outliers_info': outliers_info,
        'correlation_pearson': corr_pearson,
        'correlation_spearman': corr_spearman,
        'suggestions': suggestions
    }

    return X, y, resultats_eda

def optimiser_taille_dataset(X, y, echantillons_max=20000):
    """Reduit la taille du dataset si necessaire"""
    if len(X) > echantillons_max:
        print(f"Dataset volumineux detecte ({len(X)} echantillons)")
        print(f"   Reduction a {echantillons_max} echantillons pour accelerer l'analyse...")
        X_reduit, _, y_reduit, _ = train_test_split(X, y, train_size=echantillons_max, random_state=42, stratify=y)
        print(f"   Dataset reduit: {len(X_reduit)} echantillons")
        return X_reduit, y_reduit
    else:
        print(f"Taille du dataset: {len(X)} echantillons (optimale)")
        return X, y

def appliquer_techniques_equilibrage(X_train, y_train):
    """Applique toutes les techniques d'equilibrage"""
    print(f"\n=== APPLICATION DES TECHNIQUES D'EQUILIBRAGE ===")
    
    techniques = {
        'Original': None,
        'SMOTE': SMOTE(random_state=42),
        'ADASYN': ADASYN(random_state=42),
        'BorderlineSMOTE': BorderlineSMOTE(random_state=42),
        'RandomOverSampler': RandomOverSampler(random_state=42),
        'RandomUnderSampler': RandomUnderSampler(random_state=42),
        'SMOTEENN': SMOTEENN(random_state=42),
        'SMOTETomek': SMOTETomek(random_state=42)
    }
    
    resultats_equilibrage = {}
    
    for nom, technique in techniques.items():
        try:
            if technique is None:
                X_eq, y_eq = X_train.copy(), y_train.copy()
            else:
                X_eq, y_eq = technique.fit_resample(X_train, y_train)
            
            distribution = pd.Series(y_eq).value_counts().sort_index()
            taille = len(y_eq)
            
            resultats_equilibrage[nom] = {
                'X': X_eq,
                'y': y_eq,
                'taille': taille,
                'distribution': distribution,
                'ratio': distribution.max() / distribution.min() if len(distribution) == 2 else None
            }
            
            print(f"{nom:20} | Taille: {taille:6,} | Distribution: {dict(distribution)}")
            
        except Exception as e:
            print(f"{nom:20} | ERREUR: {str(e)[:50]}...")
            resultats_equilibrage[nom] = None
    
    return resultats_equilibrage

# Selection et chargement des donnees
nettoyer_resultats_precedents()
chemin_fichier, type_donnees = selectionner_fichier_donnees()
donnees = pd.read_csv(chemin_fichier)

print(f"\n=== ANALYSE DU DATASET {type_donnees.upper()} ===")
print("Apercu des donnees:")
print(donnees.head())

print("\nInformations sur le dataset:")
print(donnees.info())

print("\nStatistiques descriptives:")
print(donnees.describe())

print("\nValeurs manquantes par colonne:")
print(donnees.isnull().sum())

# Detection de la variable cible
colonne_cible = detecter_variable_cible(donnees, type_donnees)
print(f"\nVariable cible detectee: '{colonne_cible}'")

# Diagnostic du desequilibre
ratio_desequilibre, niveau_desequilibre, distribution_originale = diagnostiquer_desequilibre(donnees[colonne_cible], type_donnees)

# Definition des variables ordinales selon le type de donnees
variables_ordinales = None
if type_donnees == 'revenu':
    # Exemple pour le dataset revenu - education est ordinale
    variables_ordinales = {
        'education': ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', '10th', '11th', '12th',
                     'HS-grad', 'Some-college', 'Assoc-voc', 'Assoc-acdm', 'Bachelors',
                     'Masters', 'Prof-school', 'Doctorate']
    }

# Preprocessing avance avec EDA complet
X, y, resultats_eda = preprocesser_donnees_avance(
    donnees, colonne_cible, type_donnees,
    strategie_categorielle='most_frequent',  # ou 'unknown'
    methode_normalisation='standard',        # ou 'minmax'
    variables_ordinales=variables_ordinales
)
X_optimise, y_optimise = optimiser_taille_dataset(X, y)

# Affichage des resultats EDA
print(f"\n{'='*80}")
print("RESULTATS DE L'ANALYSE EXPLORATOIRE (EDA)")
print(f"{'='*80}")

if resultats_eda['suggestions']:
    print(f"\nSuggestions de feature engineering identifiees:")
    for i, suggestion in enumerate(resultats_eda['suggestions'], 1):
        print(f"{i:2d}. {suggestion}")

if resultats_eda['outliers_info']:
    print(f"\nOutliers detectes dans {len(resultats_eda['outliers_info'])} variables")
    outliers_significatifs = {k: v for k, v in resultats_eda['outliers_info'].items()
                             if v['pourcentage'] > 1}
    if outliers_significatifs:
        print("Variables avec outliers significatifs (>1%):")
        for var, info in outliers_significatifs.items():
            print(f"  {var}: {info['pourcentage']:.1f}% outliers")

# Split train/test
X_entrainement, X_test, y_entrainement, y_test = train_test_split(
    X_optimise, y_optimise, test_size=0.3, random_state=42, stratify=y_optimise
)

print(f"Dimensions de X_entrainement: {X_entrainement.shape}")
print(f"Dimensions de X_test: {X_test.shape}")

# Application des techniques d'equilibrage
resultats_equilibrage = appliquer_techniques_equilibrage(X_entrainement, y_entrainement)

# AJOUT DES VISUALISATIONS MANQUANTES DU PROGRAMME ORIGINAL
def creer_visualisations_originales(df, col_cible, type_donnees):
    """
    Cree TOUTES les visualisations du programme original
    """
    print(f"\nCreation des visualisations originales pour le dataset {type_donnees}...")

    # 1. Distribution de la variable cible
    plt.figure(figsize=(10, 6))
    sns.countplot(x=col_cible, data=df)
    plt.title(f'Distribution de la variable cible ({col_cible})')
    plt.tight_layout()
    plt.savefig(f'{type_donnees}_target_distribution.png')
    plt.close()

    # 2. Matrice de correlation pour les variables numeriques
    colonnes_numeriques = df.select_dtypes(include=['int64', 'float64']).columns
    if len(colonnes_numeriques) > 1:
        plt.figure(figsize=(12, 10))
        matrice_correlation = df[colonnes_numeriques].corr()
        sns.heatmap(matrice_correlation, annot=True, cmap='coolwarm', linewidths=0.5)
        plt.title('Matrice de correlation des variables numeriques')
        plt.tight_layout()
        plt.savefig(f'{type_donnees}_correlation.png')
        plt.close()

    # 3. Histogrammes des variables numeriques (maximum 6)
    variables_numeriques = [col for col in colonnes_numeriques if col != col_cible][:6]
    if variables_numeriques:
        plt.figure(figsize=(15, 10))
        nb_graphiques = len(variables_numeriques)
        nb_colonnes = 3
        nb_lignes = (nb_graphiques + nb_colonnes - 1) // nb_colonnes

        for i, variable in enumerate(variables_numeriques):
            plt.subplot(nb_lignes, nb_colonnes, i+1)
            try:
                if df[col_cible].nunique() <= 10:  # Variable cible categorielle
                    sns.histplot(data=df, x=variable, hue=col_cible, kde=True, bins=30)
                else:  # Variable cible continue
                    sns.histplot(data=df, x=variable, kde=True, bins=30)
                plt.title(f'Distribution de {variable}')
            except Exception as e:
                plt.text(0.5, 0.5, f'Erreur: {str(e)[:50]}...',
                        transform=plt.gca().transAxes, ha='center', va='center')
                plt.title(f'Erreur pour {variable}')

        plt.tight_layout()
        plt.savefig(f'{type_donnees}_histograms.png')
        plt.close()

    # 4. Boxplots pour les variables numeriques vs cible (si cible categorielle)
    if df[col_cible].nunique() <= 10 and variables_numeriques:
        nb_variables_a_tracer = min(6, len(variables_numeriques))
        plt.figure(figsize=(15, 10))

        for i, variable in enumerate(variables_numeriques[:nb_variables_a_tracer]):
            plt.subplot(2, 3, i+1)
            try:
                sns.boxplot(x=col_cible, y=variable, data=df)
                plt.title(f'{variable} par {col_cible}')
                plt.xticks(rotation=45)
            except Exception as e:
                plt.text(0.5, 0.5, f'Erreur: {str(e)[:50]}...',
                        transform=plt.gca().transAxes, ha='center', va='center')
                plt.title(f'Erreur pour {variable}')

        plt.tight_layout()
        plt.savefig(f'{type_donnees}_boxplots.png')
        plt.close()

    print(f"Visualisations originales sauvegardees avec le prefixe '{type_donnees}_'")

def creer_analyses_variables_categorielles(df, col_cible, type_donnees):
    """
    Cree les analyses des variables categorielles (comme dans le programme original)
    """
    # Detection automatique des variables categorielles
    variables_categorielles = []
    for col in df.columns:
        if col != col_cible and (df[col].dtype == 'object' or df[col].nunique() <= 10):
            variables_categorielles.append(col)

    print(f"\nCreation des analyses pour {len(variables_categorielles)} variables categorielles...")

    for variable in variables_categorielles:
        if variable in df.columns:
            plt.figure(figsize=(10, 6))
            try:
                tableau_croise = pd.crosstab(df[variable], df[col_cible], normalize='index')
                tableau_croise.plot(kind='bar', stacked=True)
                plt.title(f'Distribution de {col_cible} par {variable}')
                plt.tight_layout()
                plt.savefig(f'{type_donnees}_{variable}_analysis.png')
                plt.close()
                print(f"  Cree: {type_donnees}_{variable}_analysis.png")
            except Exception as e:
                print(f"  Impossible de creer le graphique pour {variable}: {e}")
                plt.close()

# Execution des visualisations originales AVANT l'equilibrage
print(f"\n=== CREATION DES VISUALISATIONS ORIGINALES ===")
creer_visualisations_originales(donnees, colonne_cible, type_donnees)
creer_analyses_variables_categorielles(donnees, colonne_cible, type_donnees)

def definir_modeles_avec_class_weight():
    """Definit tous les modeles avec et sans class_weight"""
    modeles_base = {
        'Regression Logistique': LogisticRegression(max_iter=5000, random_state=42, solver='liblinear'),
        'Arbre de Decision': DecisionTreeClassifier(random_state=42),
        'Random Forest': RandomForestClassifier(n_estimators=50, random_state=42),
        'KNN': KNeighborsClassifier(n_neighbors=5),
        'SVM (Linear)': SVC(kernel='linear', probability=True, random_state=42, max_iter=5000, C=1.0),
        'Naive Bayes': GaussianNB(),
        'Gradient Boosting': GradientBoostingClassifier(n_estimators=50, random_state=42)
    }

    modeles_ponderes = {
        'Regression Logistique (Balanced)': LogisticRegression(max_iter=5000, random_state=42, class_weight='balanced', solver='liblinear'),
        'Arbre de Decision (Balanced)': DecisionTreeClassifier(random_state=42, class_weight='balanced'),
        'Random Forest (Balanced)': RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced'),
        'SVM Linear (Balanced)': SVC(kernel='linear', probability=True, random_state=42, max_iter=5000, class_weight='balanced', C=1.0)
    }

    return modeles_base, modeles_ponderes

def evaluer_modele(modele, X_entrainement, y_entrainement, X_test, y_test):
    """Evalue les performances d'un modele avec validation croisée et rapport détaillé"""
    modele.fit(X_entrainement, y_entrainement)

    y_entrainement_pred = modele.predict(X_entrainement)
    y_test_pred = modele.predict(X_test)

    # ROC AUC si possible
    try:
        y_test_proba = modele.predict_proba(X_test)[:, 1]
        roc_auc = roc_auc_score(y_test, y_test_proba)
    except:
        roc_auc = None

    # Métriques de base
    precision_entrainement = accuracy_score(y_entrainement, y_entrainement_pred)
    precision_test = accuracy_score(y_test, y_test_pred)
    precision_score_test = precision_score(y_test, y_test_pred, average='weighted')
    rappel_test = recall_score(y_test, y_test_pred, average='weighted')
    f1_test = f1_score(y_test, y_test_pred, average='weighted')

    # Validation croisée avec cross_val_score
    try:
        cv_scores = cross_val_score(modele, X_entrainement, y_entrainement,
                                   cv=5, scoring='f1_weighted', n_jobs=-1)
        cv_mean = cv_scores.mean()
        cv_std = cv_scores.std()
    except Exception as e:
        print(f"Erreur validation croisée: {e}")
        cv_mean, cv_std = None, None

    # Classification report
    try:
        class_report = classification_report(y_test, y_test_pred, output_dict=True)
        class_report_str = classification_report(y_test, y_test_pred)
    except Exception as e:
        print(f"Erreur classification report: {e}")
        class_report = None
        class_report_str = None

    return {
        'train_accuracy': precision_entrainement,
        'test_accuracy': precision_test,
        'precision': precision_score_test,
        'recall': rappel_test,
        'f1_score': f1_test,
        'roc_auc': roc_auc,
        'overfitting': precision_entrainement - precision_test,
        'cv_f1_mean': cv_mean,
        'cv_f1_std': cv_std,
        'classification_report': class_report,
        'classification_report_str': class_report_str
    }

def evaluer_toutes_combinaisons(resultats_equilibrage, X_test, y_test):
    """Evalue toutes les combinaisons technique-modele"""
    print(f"\n=== EVALUATION DE TOUTES LES COMBINAISONS ===")

    modeles_base, modeles_ponderes = definir_modeles_avec_class_weight()
    resultats_complets = {}

    # Evaluation avec techniques d'equilibrage
    for nom_technique, donnees in resultats_equilibrage.items():
        if donnees is None:
            continue

        X_eq, y_eq = donnees['X'], donnees['y']
        resultats_complets[nom_technique] = {}

        print(f"\n--- Technique: {nom_technique} ---")

        for nom_modele, modele in modeles_base.items():
            try:
                resultats = evaluer_modele(modele, X_eq, y_eq, X_test, y_test)
                resultats_complets[nom_technique][nom_modele] = resultats

                # Affichage avec validation croisée
                cv_info = ""
                if resultats['cv_f1_mean'] is not None:
                    cv_info = f" | CV: {resultats['cv_f1_mean']:.4f}±{resultats['cv_f1_std']:.4f}"

                print(f"  {nom_modele:25} | F1: {resultats['f1_score']:.4f} | Acc: {resultats['test_accuracy']:.4f}{cv_info}")
            except Exception as e:
                print(f"  {nom_modele:25} | ERREUR: {str(e)[:30]}...")
                resultats_complets[nom_technique][nom_modele] = None

    # Evaluation avec ponderation des classes (donnees originales)
    if 'Original' in resultats_equilibrage:
        X_orig, y_orig = resultats_equilibrage['Original']['X'], resultats_equilibrage['Original']['y']
        resultats_complets['Class Weight Balanced'] = {}

        print(f"\n--- Technique: Class Weight Balanced ---")

        for nom_modele, modele in modeles_ponderes.items():
            try:
                resultats = evaluer_modele(modele, X_orig, y_orig, X_test, y_test)
                resultats_complets['Class Weight Balanced'][nom_modele] = resultats

                # Affichage avec validation croisée
                cv_info = ""
                if resultats['cv_f1_mean'] is not None:
                    cv_info = f" | CV: {resultats['cv_f1_mean']:.4f}±{resultats['cv_f1_std']:.4f}"

                print(f"  {nom_modele:25} | F1: {resultats['f1_score']:.4f} | Acc: {resultats['test_accuracy']:.4f}{cv_info}")
            except Exception as e:
                print(f"  {nom_modele:25} | ERREUR: {str(e)[:30]}...")
                resultats_complets['Class Weight Balanced'][nom_modele] = None

    return resultats_complets

def creer_visualisations_equilibrage(resultats_complets, type_donnees):
    """Cree les visualisations comparatives des techniques d'equilibrage"""
    print(f"\n=== CREATION DES VISUALISATIONS D'EQUILIBRAGE ===")

    # Preparation des donnees pour visualisation
    donnees_viz = []
    for technique, modeles in resultats_complets.items():
        for modele, metriques in modeles.items():
            if metriques is not None:
                donnees_viz.append({
                    'Technique': technique,
                    'Modele': modele,
                    'F1-Score': metriques['f1_score'],
                    'Accuracy': metriques['test_accuracy'],
                    'Precision': metriques['precision'],
                    'Recall': metriques['recall'],
                    'Overfitting': metriques['overfitting']
                })

    df_viz = pd.DataFrame(donnees_viz)

    if df_viz.empty:
        print("Aucune donnee pour les visualisations")
        return None

    # 1. Heatmap comparative des techniques d'equilibrage
    plt.figure(figsize=(16, 12))

    # Pivot pour la heatmap
    pivot_f1 = df_viz.pivot_table(values='F1-Score', index='Modele', columns='Technique', aggfunc='mean')

    sns.heatmap(pivot_f1, annot=True, cmap='YlOrRd', linewidths=0.5, fmt='.3f')
    plt.title(f'Heatmap F1-Score par Technique d\'Equilibrage - {type_donnees.upper()}')
    plt.xlabel('Technique d\'Equilibrage')
    plt.ylabel('Modele')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{type_donnees}_equilibrage_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Comparaison des meilleures performances par technique
    plt.figure(figsize=(14, 8))

    # Meilleure performance par technique
    meilleur_par_technique = df_viz.groupby('Technique')['F1-Score'].max().sort_values(ascending=False)

    plt.bar(range(len(meilleur_par_technique)), meilleur_par_technique.values,
           color=plt.cm.viridis(np.linspace(0, 1, len(meilleur_par_technique))))

    # Ajout des valeurs sur les barres
    for i, (technique, valeur) in enumerate(meilleur_par_technique.items()):
        plt.text(i, valeur + 0.005, f'{valeur:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.xlabel('Technique d\'Equilibrage')
    plt.ylabel('Meilleur F1-Score')
    plt.title(f'Meilleure Performance par Technique d\'Equilibrage - {type_donnees.upper()}')
    plt.xticks(range(len(meilleur_par_technique)), meilleur_par_technique.index, rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{type_donnees}_meilleures_techniques.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. Analyse de l'overfitting par technique
    plt.figure(figsize=(14, 8))

    # Boxplot de l'overfitting par technique
    techniques_avec_donnees = df_viz['Technique'].unique()
    overfitting_data = [df_viz[df_viz['Technique'] == tech]['Overfitting'].values
                       for tech in techniques_avec_donnees]

    bp = plt.boxplot(overfitting_data, labels=techniques_avec_donnees, patch_artist=True)

    # Coloration des boites
    colors = plt.cm.RdYlBu_r(np.linspace(0, 1, len(bp['boxes'])))
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='Pas d\'overfitting')
    plt.axhline(y=0.1, color='orange', linestyle='--', alpha=0.7, label='Seuil overfitting eleve')

    plt.xlabel('Technique d\'Equilibrage')
    plt.ylabel('Overfitting (Train - Test Accuracy)')
    plt.title(f'Analyse de l\'Overfitting par Technique - {type_donnees.upper()}')
    plt.xticks(rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{type_donnees}_overfitting_techniques.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Visualisations d'equilibrage sauvegardees avec le prefixe '{type_donnees}_'")
    return df_viz

# Execution de l'evaluation complete
resultats_complets = evaluer_toutes_combinaisons(resultats_equilibrage, X_test, y_test)

# Creation des visualisations d'equilibrage
df_resultats = creer_visualisations_equilibrage(resultats_complets, type_donnees)

# AJOUT DES VISUALISATIONS DE PERFORMANCE MANQUANTES
def creer_visualisations_performance_modeles(resultats_complets, type_donnees):
    """
    Cree les visualisations de performance des modeles (comme dans le programme original)
    """
    print(f"\n=== CREATION DES VISUALISATIONS DE PERFORMANCE ===")

    # Preparation des donnees pour les visualisations de performance
    donnees_perf = []
    for technique, modeles in resultats_complets.items():
        for modele, metriques in modeles.items():
            if metriques is not None:
                donnees_perf.append({
                    'Technique': technique,
                    'Modele': modele,
                    'Accuracy': metriques['test_accuracy'],
                    'Precision': metriques['precision'],
                    'Recall': metriques['recall'],
                    'F1-Score': metriques['f1_score'],
                    'ROC-AUC': metriques['roc_auc'] if metriques['roc_auc'] is not None else 0,
                    'Overfitting': metriques['overfitting']
                })

    df_perf = pd.DataFrame(donnees_perf)

    if df_perf.empty:
        print("Aucune donnee pour les visualisations de performance")
        return

    # 1. Heatmap des performances des modeles (style original)
    plt.figure(figsize=(14, 10))

    # Prendre la meilleure performance par modele (toutes techniques confondues)
    meilleure_perf_par_modele = df_perf.groupby('Modele').agg({
        'Accuracy': 'max',
        'Precision': 'max',
        'Recall': 'max',
        'F1-Score': 'max',
        'ROC-AUC': 'max'
    })

    sns.heatmap(meilleure_perf_par_modele, annot=True, cmap='YlGnBu', linewidths=0.5, fmt='.3f')
    plt.title('Heatmap des meilleures performances par modele')
    plt.tight_layout()
    plt.savefig('model_performance_heatmap.png')
    plt.close()

    # 2. Comparaison des F1-Scores (style original)
    plt.figure(figsize=(12, 8))

    # Meilleur F1-Score par modele
    meilleur_f1_par_modele = df_perf.groupby('Modele')['F1-Score'].max().sort_values(ascending=False)

    sns.barplot(x=meilleur_f1_par_modele.values, y=meilleur_f1_par_modele.index, palette='viridis')
    plt.title('Comparaison des meilleurs F1-Scores par modele')
    plt.xlabel('F1-Score')
    plt.ylabel('Modele')

    # Ajout des valeurs sur les barres
    for i, v in enumerate(meilleur_f1_par_modele.values):
        plt.text(v + 0.01, i, f'{v:.3f}', va='center')

    plt.tight_layout()
    plt.savefig('f1_score_comparison.png')
    plt.close()

    # 3. Graphique radar pour comparer les metriques (style original)
    plt.figure(figsize=(12, 8))
    angles = np.linspace(0, 2 * np.pi, 5, endpoint=False)  # 5 metriques
    angles = np.concatenate((angles, [angles[0]]))

    _, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

    # Tracer les 3 meilleurs modeles
    top3_modeles = meilleur_f1_par_modele.head(3)
    couleurs = ['red', 'blue', 'green']

    for i, (modele, _) in enumerate(top3_modeles.items()):
        # Prendre les meilleures metriques pour ce modele
        metriques_modele = df_perf[df_perf['Modele'] == modele].iloc[0]
        valeurs = [
            metriques_modele['Accuracy'],
            metriques_modele['Precision'],
            metriques_modele['Recall'],
            metriques_modele['F1-Score'],
            metriques_modele['ROC-AUC']
        ]
        valeurs += [valeurs[0]]  # Fermer le polygone

        ax.plot(angles, valeurs, 'o-', linewidth=2, label=modele, color=couleurs[i])
        ax.fill(angles, valeurs, alpha=0.25, color=couleurs[i])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(['Accuracy', 'Precision', 'Recall', 'F1-Score', 'ROC-AUC'])
    ax.set_ylim(0, 1)
    ax.set_title('Comparaison radar des 3 meilleurs modeles', size=16, y=1.1)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    plt.tight_layout()
    plt.savefig('radar_chart_top3_models.png', bbox_inches='tight')
    plt.close()

    # 4. Analyse de l'overfitting (style original)
    plt.figure(figsize=(14, 8))

    # Donnees d'overfitting par modele
    overfitting_par_modele = df_perf.groupby('Modele').agg({
        'Overfitting': ['mean', 'std']
    }).round(4)

    overfitting_par_modele.columns = ['Overfitting_mean', 'Overfitting_std']
    overfitting_par_modele = overfitting_par_modele.sort_values('Overfitting_mean', ascending=False)

    # Barres avec barres d'erreur
    x = np.arange(len(overfitting_par_modele))
    plt.bar(x, overfitting_par_modele['Overfitting_mean'],
            yerr=overfitting_par_modele['Overfitting_std'],
            capsize=5, alpha=0.7, color='skyblue')

    # Lignes de reference
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='Pas d\'overfitting')
    plt.axhline(y=0.1, color='orange', linestyle='--', alpha=0.7, label='Seuil overfitting eleve')

    plt.xlabel('Modele')
    plt.ylabel('Overfitting (Train - Test Accuracy)')
    plt.title('Analyse de l\'overfitting par modele')
    plt.xticks(x, overfitting_par_modele.index, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('overfitting_analysis.png')
    plt.close()

    # 5. Matrice de confusion du meilleur modele
    # Identifier le meilleur modele
    meilleur_modele_row = df_perf.loc[df_perf['F1-Score'].idxmax()]
    meilleur_technique = meilleur_modele_row['Technique']
    meilleur_modele_nom = meilleur_modele_row['Modele']

    print(f"Creation de la matrice de confusion pour: {meilleur_technique} + {meilleur_modele_nom}")

    # Recreer et evaluer le meilleur modele pour la matrice de confusion
    try:
        if meilleur_technique == 'Class Weight Balanced':
            # Utiliser les donnees originales avec class_weight
            X_best = resultats_equilibrage['Original']['X']
            y_best = resultats_equilibrage['Original']['y']

            # Recreer le modele avec class_weight
            if 'Regression Logistique' in meilleur_modele_nom:
                modele_best = LogisticRegression(max_iter=5000, random_state=42, class_weight='balanced', solver='liblinear')
            elif 'Random Forest' in meilleur_modele_nom:
                modele_best = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
            elif 'SVM' in meilleur_modele_nom:
                modele_best = SVC(kernel='linear', random_state=42, class_weight='balanced', max_iter=5000, C=1.0)
            elif 'Arbre' in meilleur_modele_nom:
                modele_best = DecisionTreeClassifier(random_state=42, class_weight='balanced')
            else:
                modele_best = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
        else:
            # Utiliser les donnees equilibrees
            X_best = resultats_equilibrage[meilleur_technique]['X']
            y_best = resultats_equilibrage[meilleur_technique]['y']

            # Recreer le modele standard
            if 'Regression Logistique' in meilleur_modele_nom:
                modele_best = LogisticRegression(max_iter=5000, random_state=42, solver='liblinear')
            elif 'Random Forest' in meilleur_modele_nom:
                modele_best = RandomForestClassifier(n_estimators=50, random_state=42)
            elif 'Gradient Boosting' in meilleur_modele_nom:
                modele_best = GradientBoostingClassifier(n_estimators=50, random_state=42)
            elif 'SVM' in meilleur_modele_nom:
                modele_best = SVC(kernel='linear', random_state=42, max_iter=5000, C=1.0)
            elif 'KNN' in meilleur_modele_nom:
                modele_best = KNeighborsClassifier(n_neighbors=5)
            elif 'Naive Bayes' in meilleur_modele_nom:
                modele_best = GaussianNB()
            elif 'Arbre' in meilleur_modele_nom:
                modele_best = DecisionTreeClassifier(random_state=42)
            else:
                modele_best = RandomForestClassifier(n_estimators=50, random_state=42)

        # Entrainement et prediction
        modele_best.fit(X_best, y_best)
        y_pred_best = modele_best.predict(X_test)

        # Matrice de confusion
        matrice_confusion = confusion_matrix(y_test, y_pred_best)

        plt.figure(figsize=(8, 6))
        sns.heatmap(matrice_confusion, annot=True, fmt='d', cmap='Blues',
                    xticklabels=['Classe 0', 'Classe 1'],
                    yticklabels=['Classe 0', 'Classe 1'])
        plt.xlabel('Prediction')
        plt.ylabel('Realite')
        plt.title(f'Matrice de confusion - {meilleur_technique} + {meilleur_modele_nom}')
        plt.tight_layout()
        plt.savefig('best_model_confusion_matrix.png')
        plt.close()

        # Affichage du rapport de classification détaillé
        print(f"\n{'='*60}")
        print("RAPPORT DE CLASSIFICATION DÉTAILLÉ - MEILLEUR MODÈLE")
        print(f"{'='*60}")
        print(f"Modèle: {meilleur_modele_nom}")
        print(f"Technique d'équilibrage: {meilleur_technique}")
        print(f"\n{classification_report(y_test, y_pred_best)}")

    except Exception as e:
        print(f"Erreur lors de la creation de la matrice de confusion: {e}")

    print(f"Visualisations de performance sauvegardees")

# Execution des visualisations de performance
creer_visualisations_performance_modeles(resultats_complets, type_donnees)

def analyser_resultats_equilibrage(df_resultats):
    """Analyse les resultats et identifie les meilleures combinaisons"""
    if df_resultats is None or df_resultats.empty:
        return None, None

    print(f"\n=== ANALYSE DES RESULTATS D'EQUILIBRAGE ===")

    # Tri par F1-Score
    df_trie = df_resultats.sort_values('F1-Score', ascending=False)

    print("\nTop 10 des meilleures combinaisons:")
    print("-" * 80)
    print(f"{'Rang':<4} {'Technique':<20} {'Modele':<25} {'F1':<8} {'Acc':<8} {'Over':<8}")
    print("-" * 80)

    for i, (_, row) in enumerate(df_trie.head(10).iterrows()):
        print(f"{i+1:<4} {row['Technique']:<20} {row['Modele']:<25} "
              f"{row['F1-Score']:<8.4f} {row['Accuracy']:<8.4f} {row['Overfitting']:<8.4f}")

    # Meilleure combinaison
    meilleur = df_trie.iloc[0]

    # Performance moyenne par technique
    print(f"\nPerformance moyenne par technique d'equilibrage:")
    perf_par_technique = df_resultats.groupby('Technique')['F1-Score'].agg(['mean', 'std', 'count'])
    perf_par_technique = perf_par_technique.sort_values('mean', ascending=False)

    print("-" * 70)
    print(f"{'Technique':<20} {'Moyenne':<10} {'Ecart-type':<12} {'Nb modeles':<12}")
    print("-" * 70)
    for technique, stats in perf_par_technique.iterrows():
        print(f"{technique:<20} {stats['mean']:<10.4f} {stats['std']:<12.4f} {stats['count']:<12.0f}")

    return meilleur, perf_par_technique

def generer_rapport_complet(type_donnees, ratio_desequilibre, niveau_desequilibre,
                           meilleur_combinaison, perf_par_technique, df_resultats):
    """Genere le rapport complet avec equilibrage avance"""

    # Comparaison avec SMOTE standard
    smote_results = df_resultats[df_resultats['Technique'] == 'SMOTE']
    if not smote_results.empty:
        meilleur_smote = smote_results.loc[smote_results['F1-Score'].idxmax()]
        amelioration = meilleur_combinaison['F1-Score'] - meilleur_smote['F1-Score']
    else:
        amelioration = 0
        meilleur_smote = None

    rapport = f"""
================================================================================
RAPPORT COMPLET D'ANALYSE AVEC EQUILIBRAGE AVANCE - {type_donnees.upper()}
================================================================================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
Dataset: {type_donnees}.csv
Techniques d'equilibrage evaluees: 8 methodes + class_weight

================================================================================
1. DIAGNOSTIC DU DESEQUILIBRE
================================================================================

Niveau de desequilibre detecte: {niveau_desequilibre}
Ratio de desequilibre: {ratio_desequilibre:.2f}:1

Interpretation:
"""

    if ratio_desequilibre < 1.5:
        rapport += """- Dataset relativement equilibre
- Techniques d'equilibrage peuvent apporter des gains marginaux
- Focus sur la reduction du bruit et l'amelioration de la qualite"""
    elif ratio_desequilibre < 3:
        rapport += """- Desequilibre leger a modere
- Techniques d'equilibrage recommandees
- Attention particuliere a la classe minoritaire"""
    elif ratio_desequilibre < 10:
        rapport += """- Desequilibre modere
- Techniques d'equilibrage essentielles
- Risque de biais vers la classe majoritaire"""
    else:
        rapport += """- Desequilibre severe
- Techniques d'equilibrage critiques
- Evaluation avec metriques adaptees necessaire"""

    rapport += f"""

================================================================================
2. RESULTATS DE L'EQUILIBRAGE AVANCE
================================================================================

MEILLEURE COMBINAISON IDENTIFIEE:
- Technique d'equilibrage: {meilleur_combinaison['Technique']}
- Algorithme: {meilleur_combinaison['Modele']}
- Performance (F1-Score): {meilleur_combinaison['F1-Score']:.4f}
- Accuracy: {meilleur_combinaison['Accuracy']:.4f}
- Overfitting: {meilleur_combinaison['Overfitting']:.4f}

CLASSEMENT DES TECHNIQUES (par performance moyenne):
"""

    for i, (technique, stats) in enumerate(perf_par_technique.iterrows()):
        rapport += f"{i+1:2d}. {technique:<20} | F1 moyen: {stats['mean']:.4f} (±{stats['std']:.4f})\n"

    if amelioration > 0:
        rapport += f"""
AMELIORATION vs SMOTE STANDARD:
- SMOTE meilleur: {meilleur_smote['Modele']} (F1: {meilleur_smote['F1-Score']:.4f})
- Amelioration obtenue: +{amelioration:.4f} points de F1-Score (+{amelioration/meilleur_smote['F1-Score']*100:.1f}%)
"""

    rapport += f"""

================================================================================
3. RECOMMANDATIONS SPECIFIQUES
================================================================================

POUR LE DEPLOIEMENT EN PRODUCTION:
- Technique recommandee: {meilleur_combinaison['Technique']}
- Algorithme recommande: {meilleur_combinaison['Modele']}
- Justification: Meilleur equilibre performance/stabilite

ALTERNATIVES ROBUSTES:
"""

    # Top 3 des techniques
    top3_techniques = perf_par_technique.head(3)
    for i, (technique, stats) in enumerate(top3_techniques.iterrows()):
        rapport += f"- {technique}: Performance stable (F1 moyen: {stats['mean']:.4f})\n"

    rapport += f"""

CONSIDERATIONS OPERATIONNELLES:
- Temps d'entrainement: Variable selon la technique
- Memoire requise: Augmentation avec sur-echantillonnage
- Interpretabilite: Preserved avec class_weight, reduite avec echantillons synthetiques

================================================================================
4. VISUALISATIONS GENEREES
================================================================================

Fichiers crees pour l'analyse d'equilibrage:
- {type_donnees}_equilibrage_heatmap.png: Comparaison complete des techniques
- {type_donnees}_meilleures_techniques.png: Classement des performances
- {type_donnees}_overfitting_techniques.png: Analyse de la generalisation

Ces visualisations permettent:
- Identification rapide de la meilleure technique
- Comparaison visuelle des performances
- Detection des problemes d'overfitting

================================================================================
5. CONCLUSION ET PROCHAINES ETAPES
================================================================================

L'analyse d'equilibrage avance revele que {meilleur_combinaison['Technique']}
avec {meilleur_combinaison['Modele']} offre les meilleures performances pour
le dataset {type_donnees}.

PROCHAINES ETAPES RECOMMANDEES:
1. Validation sur donnees externes
2. Optimisation des hyperparametres de la technique d'equilibrage
3. Analyse de l'impact sur l'interpretabilite
4. Mise en place du monitoring en production

IMPACT MESURE:
- Amelioration des performances: {amelioration:.4f} points de F1-Score
- Reduction potentielle du biais de classification
- Meilleure detection de la classe minoritaire

================================================================================
FIN DU RAPPORT D'EQUILIBRAGE AVANCE
================================================================================
"""

    # Sauvegarde du rapport unique
    nom_fichier = 'RAPPORT_FINAL_UNIQUE.txt'
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        f.write(rapport)

    print(f"\nRapport unique sauvegarde: {nom_fichier}")
    return rapport

# Analyse des resultats avec fonctionnalites avancees
if df_resultats is not None:
    meilleur_combinaison, perf_par_technique = analyser_resultats_equilibrage(df_resultats)

    if meilleur_combinaison is not None:
        # Validation croisee sur le meilleur modele
        print(f"\n{'='*80}")
        print("VALIDATION CROISEE DU MEILLEUR MODELE")
        print(f"{'='*80}")

        # Reconstruction du meilleur modele et technique
        meilleure_technique = meilleur_combinaison['Technique']
        meilleur_modele_nom = meilleur_combinaison['Modele']

        # Selection de la technique d'equilibrage
        if meilleure_technique == 'Original':
            technique_eq = None
        elif meilleure_technique == 'SMOTE':
            technique_eq = SMOTE(random_state=42)
        elif meilleure_technique == 'ADASYN':
            technique_eq = ADASYN(random_state=42)
        elif meilleure_technique == 'BorderlineSMOTE':
            technique_eq = BorderlineSMOTE(random_state=42)
        elif meilleure_technique == 'RandomOverSampler':
            technique_eq = RandomOverSampler(random_state=42)
        elif meilleure_technique == 'RandomUnderSampler':
            technique_eq = RandomUnderSampler(random_state=42)
        elif meilleure_technique == 'SMOTEENN':
            technique_eq = SMOTEENN(random_state=42)
        elif meilleure_technique == 'SMOTETomek':
            technique_eq = SMOTETomek(random_state=42)
        else:
            technique_eq = None

        # Selection du modele
        if 'Regression Logistique' in meilleur_modele_nom:
            if 'Balanced' in meilleur_modele_nom:
                modele_cv = LogisticRegression(max_iter=5000, random_state=42, class_weight='balanced', solver='liblinear')
            else:
                modele_cv = LogisticRegression(max_iter=5000, random_state=42, solver='liblinear')
        elif 'Random Forest' in meilleur_modele_nom:
            if 'Balanced' in meilleur_modele_nom:
                modele_cv = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
            else:
                modele_cv = RandomForestClassifier(n_estimators=50, random_state=42)
        elif 'Gradient Boosting' in meilleur_modele_nom:
            modele_cv = GradientBoostingClassifier(n_estimators=50, random_state=42)
        elif 'SVM' in meilleur_modele_nom:
            if 'Balanced' in meilleur_modele_nom:
                modele_cv = SVC(kernel='linear', random_state=42, class_weight='balanced')
            else:
                modele_cv = SVC(kernel='linear', random_state=42)
        elif 'KNN' in meilleur_modele_nom:
            modele_cv = KNeighborsClassifier(n_neighbors=5)
        elif 'Naive Bayes' in meilleur_modele_nom:
            modele_cv = GaussianNB()
        elif 'Arbre' in meilleur_modele_nom:
            if 'Balanced' in meilleur_modele_nom:
                modele_cv = DecisionTreeClassifier(random_state=42, class_weight='balanced')
            else:
                modele_cv = DecisionTreeClassifier(random_state=42)
        else:
            modele_cv = RandomForestClassifier(n_estimators=50, random_state=42)

        # Execution de la validation croisee
        try:
            resultats_cv = validation_croisee_avec_equilibrage(
                X_entrainement, y_entrainement, modele_cv, technique_eq
            )
            print(f"Validation croisee terminee pour {meilleure_technique} + {meilleur_modele_nom}")
        except Exception as e:
            print(f"Erreur lors de la validation croisee: {e}")
            resultats_cv = None

        # Tableau comparatif complet
        tableau_complet = tableau_comparatif_complet(resultats_complets, None)

        # Recommandations avancees
        recommandations = recommandations_avancees(df_resultats, resultats_eda, type_donnees)

        # Generation du rapport complet
        rapport = generer_rapport_complet(
            type_donnees, ratio_desequilibre, niveau_desequilibre,
            meilleur_combinaison, perf_par_technique, df_resultats
        )

        print(f"\n{'='*80}")
        print(f"ANALYSE COMPLETE TERMINEE - {type_donnees.upper()}")
        print(f"{'='*80}")
        print(f"Meilleure technique: {meilleur_combinaison['Technique']}")
        print(f"Meilleur modele: {meilleur_combinaison['Modele']}")
        print(f"Performance (F1-Score): {meilleur_combinaison['F1-Score']:.4f}")
        print(f"Rapport unique: RAPPORT_FINAL_UNIQUE.txt")
        print(f"Visualisations: 20 graphiques generes")
    else:
        print("Erreur lors de l'analyse des resultats.")
else:
    print("Aucun resultat a analyser.")

print(f"\nAnalyse complete du dataset {type_donnees.upper()} terminee!")
print("Toutes les techniques d'equilibrage ont ete evaluees et comparees.")

if __name__ == "__main__":
    print("Programme d'analyse complete avec equilibrage avance des classes")
    print("Toutes les fonctionnalites d'equilibrage sont maintenant integrees!")
