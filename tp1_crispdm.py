#%% 1. IMPORTATION DES BIBLIOTHÈQUES
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os
import warnings
import time
from datetime import datetime
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, cross_validate, StratifiedKFold
from sklearn.preprocessing import StandardScaler, MinMaxScaler, OneHotEncoder, OrdinalEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                            f1_score, roc_auc_score, confusion_matrix, 
                            classification_report, RocCurveDisplay)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC, LinearSVC
from sklearn.naive_bayes import GaussianNB
from imblearn.over_sampling import SMOTE, RandomOverSampler
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import make_pipeline

#%% 2. CHARGEMENT ET PRÉTRAITEMENT DES DONNÉES

def load_and_preprocess_advanced(dataset_name,
                                imputation_strategy_num='median',  # 'median', 'mean'
                                imputation_strategy_cat='most_frequent',  # 'most_frequent', 'unknown'
                                scaling_method='standard',  # 'standard', 'minmax'
                                handle_missing='impute'):  # 'impute', 'drop'
    """
    Version avancée avec toutes les options de prétraitement demandées dans l'énoncé

    Args:
        dataset_name: "Revenu" ou "Crédit"
        imputation_strategy_num: 'median' ou 'mean' pour les variables numériques
        imputation_strategy_cat: 'most_frequent' ou 'unknown' pour les catégorielles
        scaling_method: 'standard' (StandardScaler) ou 'minmax' (MinMaxScaler)
        handle_missing: 'impute' (imputer) ou 'drop' (supprimer les instances)
    """
    print(f"\n🔧 PRÉTRAITEMENT AVANCÉ - {dataset_name}")
    print(f"  - Imputation numérique: {imputation_strategy_num}")
    print(f"  - Imputation catégorielle: {imputation_strategy_cat}")
    print(f"  - Normalisation: {scaling_method}")
    print(f"  - Gestion manquantes: {handle_missing}")

    # Utiliser la fonction de base pour charger les données
    X, y, _, num_features, cat_nominal, cat_ordinal = load_and_preprocess(dataset_name)

    # Option: Supprimer les instances avec valeurs manquantes
    if handle_missing == 'drop':
        print("  ⚠️ Suppression des instances avec valeurs manquantes...")
        # Combiner X et y pour supprimer les mêmes lignes
        combined_df = pd.concat([X, y], axis=1)
        combined_df_clean = combined_df.dropna()

        print(f"  📊 Instances avant: {len(X)}")
        print(f"  📊 Instances après: {len(combined_df_clean)}")
        print(f"  📊 Instances supprimées: {len(X) - len(combined_df_clean)}")

        X = combined_df_clean.iloc[:, :-1]
        y = combined_df_clean.iloc[:, -1]

    # Configuration de l'imputation numérique
    if imputation_strategy_num == 'median':
        num_imputer = SimpleImputer(strategy='median')
    elif imputation_strategy_num == 'mean':
        num_imputer = SimpleImputer(strategy='mean')
    else:
        raise ValueError("imputation_strategy_num doit être 'median' ou 'mean'")

    # Configuration de l'imputation catégorielle
    if imputation_strategy_cat == 'most_frequent':
        cat_imputer = SimpleImputer(strategy='most_frequent')
    elif imputation_strategy_cat == 'unknown':
        cat_imputer = SimpleImputer(strategy='constant', fill_value='Unknown')
    else:
        raise ValueError("imputation_strategy_cat doit être 'most_frequent' ou 'unknown'")

    # Configuration de la normalisation
    if scaling_method == 'standard':
        scaler = StandardScaler()
    elif scaling_method == 'minmax':
        scaler = MinMaxScaler()
    else:
        raise ValueError("scaling_method doit être 'standard' ou 'minmax'")

    # Ordres pour les variables ordinales (même logique que la fonction de base)
    if dataset_name == "Revenu":
        education_order = ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th',
                          '10th', '11th', '12th', 'HS-grad', 'Some-college',
                          'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        marital_order = ['Never-married', 'Married-spouse-absent', 'Separated',
                        'Divorced', 'Widowed', 'Married-civ-spouse', 'Married-AF-spouse']
        ordinal_categories = [education_order, marital_order]
    else:
        ordinal_categories = []

    # Créer le préprocesseur avec les options choisies
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', Pipeline(steps=[
                ('imputer', num_imputer),
                ('scaler', scaler)]), num_features),
            ('nom', Pipeline(steps=[
                ('imputer', cat_imputer),
                ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))]), cat_nominal),
            ('ord', Pipeline(steps=[
                ('imputer', cat_imputer),
                ('encoder', OrdinalEncoder(categories=ordinal_categories, handle_unknown='use_encoded_value', unknown_value=-1))
            ]), cat_ordinal)
        ], remainder='drop')

    return X, y, preprocessor, num_features, cat_nominal, cat_ordinal

def load_and_preprocess(dataset_name):
    """Charge et prétraite les données selon le dataset spécifié"""
    if dataset_name == "Revenu":
        # Chargement des données Revenu
        revenu_columns = [
            'age', 'workclass', 'fnlwgt', 'education', 'education-num', 
            'marital-status', 'occupation', 'relationship', 'race', 
            'sex', 'capital-gain', 'capital-loss', 'hours-per-week', 
            'native-country', 'income'
        ]
        
        # Vérifier si le fichier existe
        if not os.path.exists("revenu.csv"):
            raise FileNotFoundError("Fichier revenu.csv introuvable. Placez-le dans le même répertoire que ce script.")
        
        # Charger seulement 10 000 lignes pour les tests initiaux
        df = pd.read_csv("revenu.csv", names=revenu_columns, na_values="?", skipinitialspace=True)
        
        # Conversion des colonnes numériques
        numeric_cols = ['age', 'fnlwgt', 'education-num', 'capital-gain', 'capital-loss', 'hours-per-week']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Nettoyage des colonnes catégorielles
        categorical_cols = ['workclass', 'education', 'marital-status', 'occupation', 
                            'relationship', 'race', 'sex', 'native-country', 'income']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Séparation features/cible
        X = df.drop('income', axis=1)
        y = df['income'].apply(lambda x: 1 if x.strip() == '>50K' else 0)
        
        # Vérification de la distribution des classes
        class_counts = y.value_counts()
        if len(class_counts) < 2:
            raise ValueError(f"La variable cible ne contient qu'une seule classe: {class_counts.index[0]}")
        
        # Définition des types de variables
        num_features = numeric_cols
        cat_nominal = ['workclass', 'occupation', 'relationship', 'race', 'sex', 'native-country']
        cat_ordinal = ['education', 'marital-status']
        
        # Ordres pour les variables ordinales
        education_order = ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', 
                           '10th', '11th', '12th', 'HS-grad', 'Some-college', 
                           'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        marital_order = ['Never-married', 'Married-spouse-absent', 'Separated', 
                         'Divorced', 'Widowed', 'Married-civ-spouse', 'Married-AF-spouse']
        
        # Pour l'OrdinalEncoder, nous avons deux colonnes, donc nous avons besoin de deux listes d'ordres
        ordinal_categories = [education_order, marital_order]
        
    elif dataset_name == "Crédit":
        # Vérifier si le fichier existe
        if not os.path.exists("credit.csv"):
            raise FileNotFoundError("Fichier credit.csv introuvable. Placez-le dans le même répertoire que ce script.")

        # Charger le fichier avec les en-têtes existants
        df = pd.read_csv("credit.csv", na_values="?", skipinitialspace=True)
        
        # Conversion des colonnes numériques (selon les vraies colonnes du fichier)
        numeric_cols = ['Age', 'Debt', 'YearsEmployed', 'CreditScore', 'Income']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Nettoyage des colonnes catégorielles
        categorical_cols = ['Gender', 'Married', 'BankCustomer', 'Industry', 'Ethnicity',
                           'PriorDefault', 'Employed', 'DriversLicense', 'Citizen', 'ZipCode', 'Approved']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # Séparation features/cible
        X = df.drop('Approved', axis=1)

        # Vérification et création de la variable cible
        if 'Approved' not in df.columns:
            raise KeyError("La colonne 'Approved' est introuvable dans le dataset Crédit")

        # Création de la variable cible
        y = df['Approved'].apply(lambda x: 1 if str(x).strip() in ['1', '1.0'] else 0)
        
        # Vérification de la distribution des classes
        class_counts = y.value_counts()
        if len(class_counts) < 2:
            raise ValueError(f"La variable cible ne contient qu'une seule classe: {class_counts.index[0]}")
        
        # Définition des types de variables
        num_features = numeric_cols
        cat_nominal = ['Gender', 'Married', 'BankCustomer', 'Industry', 'Ethnicity',
                      'PriorDefault', 'Employed', 'DriversLicense', 'Citizen', 'ZipCode']
        cat_ordinal = []  # Aucune variable ordinale dans ce dataset

        # Pas d'ordres pour les variables ordinales
        ordinal_categories = []
    
    # Préprocesseur avec gestion des valeurs inconnues
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='median')),
                ('scaler', StandardScaler())]), num_features),
            ('nom', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='most_frequent')),
                ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))]), cat_nominal),
            ('ord', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='most_frequent')),
                ('encoder', OrdinalEncoder(categories=ordinal_categories, handle_unknown='use_encoded_value', unknown_value=-1))
            ]), cat_ordinal)
        ], remainder='drop')
    
    return X, y, preprocessor, num_features, cat_nominal, cat_ordinal

#%% 2.5. FEATURE ENGINEERING
def feature_engineering(X, dataset_name):
    """Crée de nouvelles variables à partir des variables existantes"""
    print(f"\n{'='*50}")
    print(f"FEATURE ENGINEERING - DATASET {dataset_name}")
    print(f"{'='*50}\n")

    X_enhanced = X.copy()
    new_features = []

    if dataset_name == "Revenu":
        # Création de nouvelles variables pour le dataset Revenu
        if 'age' in X_enhanced.columns:
            # Groupes d'âge
            X_enhanced['age_group'] = pd.cut(X_enhanced['age'],
                                           bins=[0, 25, 35, 50, 65, 100],
                                           labels=['Jeune', 'Adulte_jeune', 'Adulte', 'Senior', 'Retraité'])
            new_features.append('age_group')

        if 'hours-per-week' in X_enhanced.columns:
            # Catégories de temps de travail
            X_enhanced['work_intensity'] = pd.cut(X_enhanced['hours-per-week'],
                                                bins=[0, 20, 40, 60, 100],
                                                labels=['Temps_partiel', 'Temps_plein', 'Surtemps', 'Workaholic'])
            new_features.append('work_intensity')

        if 'capital-gain' in X_enhanced.columns and 'capital-loss' in X_enhanced.columns:
            # Capital net
            X_enhanced['capital_net'] = X_enhanced['capital-gain'] - X_enhanced['capital-loss']
            new_features.append('capital_net')

            # Indicateur de capital
            X_enhanced['has_capital'] = ((X_enhanced['capital-gain'] > 0) |
                                       (X_enhanced['capital-loss'] > 0)).astype(int)
            new_features.append('has_capital')

    elif dataset_name == "Crédit":
        # Création de nouvelles variables pour le dataset Crédit
        if 'Age' in X_enhanced.columns:
            # Groupes d'âge
            X_enhanced['age_group'] = pd.cut(X_enhanced['Age'],
                                           bins=[0, 25, 35, 50, 65, 100],
                                           labels=['Jeune', 'Adulte_jeune', 'Adulte', 'Senior', 'Retraité'])
            new_features.append('age_group')

        if 'Debt' in X_enhanced.columns and 'Income' in X_enhanced.columns:
            # Ratio dette/revenu (en évitant la division par zéro)
            X_enhanced['debt_to_income'] = X_enhanced['Debt'] / (X_enhanced['Income'] + 1)
            new_features.append('debt_to_income')

        if 'YearsEmployed' in X_enhanced.columns and 'Age' in X_enhanced.columns:
            # Stabilité d'emploi (années d'emploi / âge)
            X_enhanced['employment_stability'] = X_enhanced['YearsEmployed'] / (X_enhanced['Age'] + 1)
            new_features.append('employment_stability')

        if 'CreditScore' in X_enhanced.columns:
            # Catégories de score de crédit
            X_enhanced['credit_category'] = pd.cut(X_enhanced['CreditScore'],
                                                 bins=[-1, 0, 5, 15, 100],
                                                 labels=['Aucun', 'Faible', 'Moyen', 'Élevé'])
            new_features.append('credit_category')

    print(f"Nouvelles variables créées: {new_features}")
    print(f"Nombre total de variables: {len(X_enhanced.columns)} (+ {len(new_features)})")

    return X_enhanced, new_features

#%% 2.6. DÉMONSTRATION DES OPTIONS DE PRÉTRAITEMENT
def demo_preprocessing_options(dataset_name="Crédit"):
    """Démontre toutes les options de prétraitement disponibles"""
    print(f"\n{'='*60}")
    print(f"DÉMONSTRATION DES OPTIONS DE PRÉTRAITEMENT - {dataset_name}")
    print(f"{'='*60}")

    # Tester toutes les combinaisons d'options
    options = [
        {
            'name': 'Configuration Standard (Défaut)',
            'imputation_num': 'median',
            'imputation_cat': 'most_frequent',
            'scaling': 'standard',
            'missing': 'impute'
        },
        {
            'name': 'Configuration avec Moyenne',
            'imputation_num': 'mean',
            'imputation_cat': 'most_frequent',
            'scaling': 'standard',
            'missing': 'impute'
        },
        {
            'name': 'Configuration avec Unknown',
            'imputation_num': 'median',
            'imputation_cat': 'unknown',
            'scaling': 'standard',
            'missing': 'impute'
        },
        {
            'name': 'Configuration avec MinMaxScaler',
            'imputation_num': 'median',
            'imputation_cat': 'most_frequent',
            'scaling': 'minmax',
            'missing': 'impute'
        },
        {
            'name': 'Configuration avec Suppression',
            'imputation_num': 'median',
            'imputation_cat': 'most_frequent',
            'scaling': 'standard',
            'missing': 'drop'
        }
    ]

    results_comparison = []

    for i, config in enumerate(options, 1):
        print(f"\n{i}. {config['name']}")
        print("-" * 50)

        try:
            X, y, preprocessor, num_feat, cat_nom, cat_ord = load_and_preprocess_advanced(
                dataset_name,
                imputation_strategy_num=config['imputation_num'],
                imputation_strategy_cat=config['imputation_cat'],
                scaling_method=config['scaling'],
                handle_missing=config['missing']
            )

            # Statistiques de base
            n_samples, n_features = X.shape
            missing_count = X.isnull().sum().sum()

            print(f"  📊 Échantillons: {n_samples}")
            print(f"  📊 Variables: {n_features}")
            print(f"  📊 Valeurs manquantes: {missing_count}")

            # Test rapide de préprocessing
            X_processed = preprocessor.fit_transform(X)
            print(f"  📊 Variables après préprocessing: {X_processed.shape[1]}")

            results_comparison.append({
                'Configuration': config['name'],
                'Échantillons': n_samples,
                'Variables_originales': n_features,
                'Variables_après_preprocessing': X_processed.shape[1],
                'Valeurs_manquantes': missing_count
            })

        except Exception as e:
            print(f"  ❌ Erreur: {e}")
            results_comparison.append({
                'Configuration': config['name'],
                'Échantillons': 'Erreur',
                'Variables_originales': 'Erreur',
                'Variables_après_preprocessing': 'Erreur',
                'Valeurs_manquantes': 'Erreur'
            })

    # Tableau comparatif
    print(f"\n{'='*60}")
    print("TABLEAU COMPARATIF DES CONFIGURATIONS")
    print(f"{'='*60}")

    comparison_df = pd.DataFrame(results_comparison)
    print(comparison_df.to_string(index=False))

    return comparison_df

#%% 3. ANALYSE EXPLORATOIRE (EDA)
def perform_eda(X, y, num_features, cat_nominal, cat_ordinal, dataset_name):
    """Réalise l'analyse exploratoire des données"""
    print(f"\n{'='*50}")
    print(f"ANALYSE EXPLORATOIRE - DATASET {dataset_name}")
    print(f"{'='*50}\n")
    
    # Création d'un DataFrame temporaire pour l'EDA
    df_eda = X.copy()
    df_eda['Target'] = y
    
    # Nettoyage des colonnes catégorielles
    categorical_cols = cat_nominal + cat_ordinal
    for col in categorical_cols:
        if col in df_eda.columns:
            df_eda[col] = df_eda[col].astype(str).str.strip()
    
    # 1. Statistiques descriptives
    print("1. STATISTIQUES DESCRIPTIVES:")
    print(df_eda.describe(include='all').T)
    
    # 2. Distribution des variables numériques
    print("\n2. DISTRIBUTION DES VARIABLES NUMÉRIQUES:")
    plt.figure(figsize=(15, 10))
    valid_num_cols = [col for col in num_features if col in df_eda.columns and df_eda[col].dtype in [np.int64, np.float64]]
    
    if valid_num_cols:
        for i, col in enumerate(valid_num_cols[:6]):  # Maximum 6 plots
            plt.subplot(2, 3, i+1)
            sns.histplot(df_eda[col].dropna(), kde=True)
            plt.title(f'Distribution de {col}')
        plt.tight_layout()
        plt.savefig(f'distributions_num_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune variable numérique valide à afficher")
    
    # 3. Distribution des variables catégorielles
    print("\n3. DISTRIBUTION DES VARIABLES CATÉGORIELLES:")
    cat_features = cat_nominal + cat_ordinal
    valid_cat_cols = [col for col in cat_features if col in df_eda.columns and (df_eda[col].dtype == object or df_eda[col].dtype.name == 'category')]
    
    if valid_cat_cols:
        plt.figure(figsize=(18, 12))
        for i, col in enumerate(valid_cat_cols[:9]):  # Maximum 9 plots
            plt.subplot(3, 3, i+1)
            # Prendre les 10 catégories les plus fréquentes
            value_counts = df_eda[col].value_counts()
            top_categories = value_counts.nlargest(10).index
            filtered_data = df_eda[df_eda[col].isin(top_categories)]
            sns.countplot(data=filtered_data, x=col, order=top_categories)
            plt.title(f'Distribution de {col}')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'distributions_cat_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune variable catégorielle valide à afficher")
    
    # 4. Distribution de la cible
    print("\n4. DISTRIBUTION DE LA CIBLE:")
    plt.figure(figsize=(8, 5))
    sns.countplot(x='Target', data=df_eda)
    plt.title(f'Distribution de la variable cible ({dataset_name})')
    plt.savefig(f'distrib_target_{dataset_name}.png')
    plt.show()
    
    # 5. Valeurs manquantes
    print("\n5. VALEURS MANQUANTES:")
    missing = df_eda.isnull().sum()
    missing = missing[missing > 0]
    if len(missing) > 0:
        print("Variables avec valeurs manquantes:")
        print(missing)
        plt.figure(figsize=(10, 5))
        sns.barplot(x=missing.index, y=missing.values)
        plt.title('Valeurs manquantes par variable')
        plt.xticks(rotation=45)
        plt.savefig(f'missing_values_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune valeur manquante détectée")
    
    # 6. Analyse des outliers (valeurs aberrantes)
    print("\n6. ANALYSE DES OUTLIERS:")
    if valid_num_cols:
        plt.figure(figsize=(15, 10))
        outliers_summary = {}

        for i, col in enumerate(valid_num_cols[:6]):
            plt.subplot(2, 3, i+1)

            # Calcul des outliers avec la méthode IQR
            Q1 = df_eda[col].quantile(0.25)
            Q3 = df_eda[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            outliers = df_eda[(df_eda[col] < lower_bound) | (df_eda[col] > upper_bound)][col]
            outliers_summary[col] = {
                'count': len(outliers),
                'percentage': len(outliers) / len(df_eda) * 100,
                'lower_bound': lower_bound,
                'upper_bound': upper_bound
            }

            # Boxplot pour visualiser les outliers
            sns.boxplot(y=df_eda[col])
            plt.title(f'Outliers - {col}\n({len(outliers)} outliers, {len(outliers)/len(df_eda)*100:.1f}%)')

        plt.tight_layout()
        plt.savefig(f'outliers_analysis_{dataset_name}.png')
        plt.show()

        # Affichage du résumé des outliers
        print("Résumé des outliers par variable:")
        for col, stats in outliers_summary.items():
            print(f"  {col}: {stats['count']} outliers ({stats['percentage']:.1f}%)")
            print(f"    Bornes: [{stats['lower_bound']:.2f}, {stats['upper_bound']:.2f}]")
    else:
        print("Aucune variable numérique pour l'analyse des outliers")

    # 7. Matrice de corrélation (uniquement pour les variables numériques)
    print("\n7. MATRICE DE CORRÉLATION (VARIABLES NUMÉRIQUES):")
    numeric_df = df_eda.select_dtypes(include=[np.number])
    if not numeric_df.empty and len(numeric_df.columns) > 1:
        plt.figure(figsize=(12, 10))
        corr = numeric_df.corr(method='spearman')
        sns.heatmap(corr, annot=True, fmt=".2f", cmap='coolwarm',
                    mask=np.triu(np.ones_like(corr, dtype=bool)))
        plt.title(f'Matrice de corrélation ({dataset_name})')
        plt.savefig(f'correlation_matrix_{dataset_name}.png')
        plt.show()

        # Analyse des corrélations fortes
        print("\nCorrélations fortes (|r| > 0.5):")
        strong_corr = []
        for i in range(len(corr.columns)):
            for j in range(i+1, len(corr.columns)):
                if abs(corr.iloc[i, j]) > 0.5:
                    strong_corr.append((corr.columns[i], corr.columns[j], corr.iloc[i, j]))

        if strong_corr:
            for var1, var2, corr_val in strong_corr:
                print(f"  {var1} ↔ {var2}: {corr_val:.3f}")
        else:
            print("  Aucune corrélation forte détectée")
    else:
        print("Pas assez de variables numériques pour la matrice de corrélation")

    return df_eda

#%% 4. MODÉLISATION ET ÉVALUATION
#%% 4. MODÉLISATION ET ÉVALUATION
def evaluate_models(X, y, preprocessor, dataset_name):
    """Entraîne, évalue et compare les modèles"""
    print(f"\n{'='*50}")
    print(f"MODÉLISATION - DATASET {dataset_name}")
    print(f"{'='*50}\n")
    
    # Vérifier la distribution des classes
    class_counts = y.value_counts()
    print(f"Distribution des classes: {class_counts.to_dict()}")
    
    # Split initial 70/30
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y)
    
    # Définition des modèles - version optimisée pour Revenu
    if dataset_name == "Revenu":
        models = {
            "Logistic Regression": LogisticRegression(max_iter=1000, class_weight='balanced', random_state=42),
            "Decision Tree": DecisionTreeClassifier(max_depth=5, random_state=42),
            "Random Forest": RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced'),
            "KNN": KNeighborsClassifier(n_neighbors=5),
            "Naive Bayes": GaussianNB(),
            "Gradient Boosting": GradientBoostingClassifier(n_estimators=50, random_state=42),
            "Linear SVM": LinearSVC(class_weight='balanced', dual=False, max_iter=1000, random_state=42)
        }
    else:
        models = {
            "Logistic Regression": LogisticRegression(max_iter=1000, class_weight='balanced', random_state=42),
            "Decision Tree": DecisionTreeClassifier(max_depth=5, random_state=42),
            "Random Forest": RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
            "KNN": KNeighborsClassifier(n_neighbors=5),
            "SVM": SVC(kernel='rbf', probability=True, random_state=42, class_weight='balanced'),
            "Naive Bayes": GaussianNB(),
            "Gradient Boosting": GradientBoostingClassifier(n_estimators=100, random_state=42)
        }
    
    # Stratégies d'équilibrage
    balance_strategies = {
        "Aucun": None,
        "SMOTE": SMOTE(random_state=42),
        "Sur-échantillonnage": RandomOverSampler(random_state=42),
        "Sous-échantillonnage": RandomUnderSampler(random_state=42)
    }
    
    # Métriques à collecter
    results = []
    
    # Évaluation de chaque modèle
    for model_name, model in models.items():
        for balance_name, balancer in balance_strategies.items():
            start_time = time.time()
            print(f"\n➤ Début: {datetime.now().strftime('%H:%M:%S')} - Modèle: {model_name} | Équilibrage: {balance_name}")
            
            try:
                # Création du pipeline
                if balancer:
                    pipeline = make_pipeline(
                        preprocessor,
                        balancer,
                        model
                    )
                else:
                    pipeline = make_pipeline(
                        preprocessor,
                        model
                    )
                
                # Entraînement
                pipeline.fit(X_train, y_train)
                
                # Prédictions
                y_pred = pipeline.predict(X_test)
                
                # Vérifier si le modèle peut prédire des probabilités
                if hasattr(pipeline[-1], "predict_proba"):
                    y_proba = pipeline.predict_proba(X_test)[:, 1]
                else:
                    # Pour LinearSVC, nous devons utiliser decision_function
                    if isinstance(model, LinearSVC):
                        decision = pipeline.decision_function(X_test)
                        y_proba = 1 / (1 + np.exp(-decision))
                    else:
                        y_proba = np.zeros(len(y_test))  # Valeur par défaut
                
                # Calcul des métriques
                metrics = {
                    "Dataset": dataset_name,
                    "Modèle": model_name,
                    "Équilibrage": balance_name,
                    "Accuracy": accuracy_score(y_test, y_pred),
                    "Precision": precision_score(y_test, y_pred, average='weighted'),
                    "Recall": recall_score(y_test, y_pred, average='weighted'),
                    "F1": f1_score(y_test, y_pred, average='weighted'),
                    "AUC": roc_auc_score(y_test, y_proba),
                    "Train_Score": pipeline.score(X_train, y_train),
                    "Test_Score": pipeline.score(X_test, y_test)
                }
                
                # Validation croisée multiple (5, 7, 10 folds comme demandé dans l'énoncé)
                cv_folds = [5, 7, 10]
                for n_folds in cv_folds:
                    try:
                        cv = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
                        scores = cross_validate(
                            pipeline, X_train, y_train, cv=cv,
                            scoring=['accuracy', 'f1_weighted'],
                            return_train_score=True
                        )

                        # Stockage des résultats CV pour chaque nombre de folds
                        cv_metrics = {
                            f"CV{n_folds}_Train_Accuracy": np.mean(scores['train_accuracy']),
                            f"CV{n_folds}_Test_Accuracy": np.mean(scores['test_accuracy']),
                            f"CV{n_folds}_Train_F1": np.mean(scores['train_f1_weighted']),
                            f"CV{n_folds}_Test_F1": np.mean(scores['test_f1_weighted']),
                            f"CV{n_folds}_Std_Accuracy": np.std(scores['test_accuracy']),
                            f"CV{n_folds}_Std_F1": np.std(scores['test_f1_weighted'])
                        }
                        metrics.update(cv_metrics)
                    except Exception as cv_error:
                        print(f"❌ Erreur CV {n_folds} folds: {str(cv_error)}")
                        metrics.update({
                            f"CV{n_folds}_Train_Accuracy": np.nan,
                            f"CV{n_folds}_Test_Accuracy": np.nan,
                            f"CV{n_folds}_Train_F1": np.nan,
                            f"CV{n_folds}_Test_F1": np.nan,
                            f"CV{n_folds}_Std_Accuracy": np.nan,
                            f"CV{n_folds}_Std_F1": np.nan
                        })
                
                results.append(metrics)
                
                # Matrice de confusion
                cm = confusion_matrix(y_test, y_pred)
                plt.figure(figsize=(6, 4))
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                            xticklabels=['Négatif', 'Positif'], 
                            yticklabels=['Négatif', 'Positif'])
                plt.title(f"Matrice de confusion\n{model_name} | {balance_name}")
                plt.ylabel('Vérité terrain')
                plt.xlabel('Prédiction')
                plt.savefig(f"cm_{dataset_name}_{model_name}_{balance_name.replace(' ', '')}.png")
                plt.close()
                
                # Courbe ROC (seulement si le modèle peut prédire des probabilités)
                if hasattr(pipeline[-1], "predict_proba") or isinstance(model, LinearSVC):
                    plt.figure(figsize=(8, 6))
                    RocCurveDisplay.from_estimator(pipeline, X_test, y_test)
                    plt.title(f"Courbe ROC - {model_name} | {balance_name}")
                    plt.savefig(f"roc_{dataset_name}_{model_name}_{balance_name.replace(' ', '')}.png")
                    plt.close()
                    
            except Exception as e:
                print(f"❌ Erreur avec {model_name} | {balance_name}: {str(e)}")
            finally:
                duration = time.time() - start_time
                print(f"✓ Fin: {datetime.now().strftime('%H:%M:%S')} - Durée: {duration:.2f}s")
    
    # Conversion des résultats en DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # Sauvegarde des résultats
        results_df.to_csv(f"resultats_modeles_{dataset_name}.csv", index=False)
        
        # Affichage des meilleurs modèles
        print("\nMEILLEURS MODÈLES PAR MÉTRIQUE:")
        for metric in ['Accuracy', 'F1', 'AUC']:
            if metric in results_df.columns:
                best = results_df.nlargest(3, metric)
                print(f"\n▶ {metric}:")
                print(best[['Modèle', 'Équilibrage', metric, 'Train_Score', 'Test_Score']])

        # Analyse d'overfitting
        results_df['Overfitting_Gap'] = results_df['Train_Score'] - results_df['Test_Score']
        print("\nANALYSE D'OVERFITTING:")
        print(results_df[['Modèle', 'Équilibrage', 'Train_Score', 'Test_Score', 'Overfitting_Gap']])

        # Analyse comparative de la validation croisée
        print("\nANALYSE COMPARATIVE VALIDATION CROISÉE:")
        cv_columns = [col for col in results_df.columns if col.startswith('CV') and 'Test_F1' in col]
        if cv_columns:
            print("Comparaison des scores F1 par validation croisée:")
            cv_comparison = results_df[['Modèle', 'Équilibrage'] + cv_columns].copy()

            # Calculer la moyenne des CV pour chaque modèle
            cv_comparison['CV_Mean_F1'] = cv_comparison[cv_columns].mean(axis=1)
            cv_comparison['CV_Std_F1'] = cv_comparison[cv_columns].std(axis=1)

            # Afficher les meilleurs modèles selon la CV moyenne
            best_cv = cv_comparison.nlargest(5, 'CV_Mean_F1')
            print("\nTop 5 modèles selon la validation croisée moyenne:")
            print(best_cv[['Modèle', 'Équilibrage', 'CV_Mean_F1', 'CV_Std_F1']])

            # Analyse de la stabilité (faible écart-type = plus stable)
            most_stable = cv_comparison.nsmallest(5, 'CV_Std_F1')
            print("\nTop 5 modèles les plus stables (faible variance CV):")
            print(most_stable[['Modèle', 'Équilibrage', 'CV_Mean_F1', 'CV_Std_F1']])

        return results_df
    else:
        print("❌ Aucun résultat à afficher - tous les modèles ont échoué")
        return None

#%% 5. FONCTION PRINCIPALE
def main(datasets=None):
    """Exécute le pipeline complet pour les datasets spécifiés"""
    # Configuration des styles
    sns.set_style("whitegrid")
    plt.rcParams['font.size'] = 12
    plt.rcParams['figure.figsize'] = (10, 6)
    plt.rcParams['savefig.dpi'] = 300
    
    # Déterminer les datasets à traiter
    if datasets is None:
        datasets_to_process = ["Revenu", "Crédit"]
    else:
        datasets_to_process = datasets
    
    # Créer un dossier pour les résultats s'il n'existe pas
    if not os.path.exists('results'):
        os.makedirs('results')
    
    # Traitement pour chaque dataset
    for dataset in datasets_to_process:
        print(f"\n{'='*50}")
        print(f"TRAITEMENT DU DATASET: {dataset}")
        print(f"{'='*50}")
        
        try:
            # Chargement et prétraitement
            X, y, preprocessor, num, nom, ord = load_and_preprocess(dataset)

            # Feature engineering
            X_enhanced, new_features = feature_engineering(X, dataset)

            # Mise à jour du préprocesseur si de nouvelles variables catégorielles ont été créées
            if new_features:
                # Identifier les nouvelles variables catégorielles
                new_cat_features = []
                for feat in new_features:
                    if feat in X_enhanced.columns and X_enhanced[feat].dtype == 'object':
                        new_cat_features.append(feat)

                # Mettre à jour les listes de variables
                updated_nom = nom + new_cat_features
                updated_num = [col for col in X_enhanced.columns if col in num and col in X_enhanced.columns]

                # Recréer le préprocesseur avec les nouvelles variables
                preprocessor = ColumnTransformer(
                    transformers=[
                        ('num', Pipeline(steps=[
                            ('imputer', SimpleImputer(strategy='median')),
                            ('scaler', StandardScaler())]), updated_num),
                        ('nom', Pipeline(steps=[
                            ('imputer', SimpleImputer(strategy='most_frequent')),
                            ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))]), updated_nom),
                        ('ord', Pipeline(steps=[
                            ('imputer', SimpleImputer(strategy='most_frequent')),
                            ('encoder', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))
                        ]), ord)
                    ], remainder='drop')
            else:
                X_enhanced = X

            # Analyse exploratoire
            df_eda = perform_eda(X_enhanced, y, num, nom, ord, dataset)

            # Modélisation et évaluation avec les variables enrichies
            results = evaluate_models(X_enhanced, y, preprocessor, dataset)
            
            # Déplacer les résultats dans le dossier
            for file in os.listdir('.'):
                if file.endswith('.png') or file.endswith('.csv'):
                    if file.startswith(('distrib_', 'cm_', 'roc_', 'resultats_', 'boxplot_', 'correlation_', 'missing_')):
                        os.rename(file, os.path.join('results', file))
            
            # Synthèse des résultats
            if results is not None:
                print(f"\nSYNTHÈSE POUR {dataset}:")
                print(f"- Meilleur modèle F1: {results.nlargest(1, 'F1')[['Modèle', 'Équilibrage']].values[0]}")
                
                if 'AUC' in results.columns:
                    best_auc = results.nlargest(1, 'AUC')[['Modèle', 'Équilibrage']].values[0]
                    print(f"- Meilleur modèle AUC: {best_auc}")
                
                worst_overfitting = results.nlargest(1, 'Overfitting_Gap')[['Modèle', 'Équilibrage', 'Overfitting_Gap']].values[0]
                print(f"- Plus grand écart d'overfitting: {worst_overfitting}")
            
        except Exception as e:
            print(f"\n❌ ERREUR lors du traitement du dataset {dataset}: {str(e)}")
            continue

#%% EXÉCUTION
if __name__ == "__main__":
    # Gestion des arguments en ligne de commande
    datasets = None
    demo_mode = False

    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg == 'revenu':
            datasets = ["Revenu"]
        elif arg == 'credit':
            datasets = ["Crédit"]
        else:
            print("Usage: python tp1_crispdm.py [revenu|credit] [--demo-preprocessing]")
            sys.exit(1)

        # Vérifier l'option de démonstration
        if len(sys.argv) > 2 and sys.argv[2] == "--demo-preprocessing":
            demo_mode = True

    if demo_mode:
        print("🧪 MODE DÉMONSTRATION DES OPTIONS DE PRÉTRAITEMENT")
        for dataset in datasets:
            demo_preprocessing_options(dataset)
    else:
        main(datasets)