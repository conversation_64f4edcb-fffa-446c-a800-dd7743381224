#!/usr/bin/env python3
"""
INF5082 - TP1 : Analyse Exploratoire et Modélisation de Données
Méthodologie CRISP-DM - Version Refactorisée (VRAIE RÉDUCTION)

Auteur: [Votre nom]
Date: 2025-06-21
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder, MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, 
                           roc_auc_score, confusion_matrix, classification_report, roc_curve)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from imblearn.over_sampling import SMOTE
import warnings
import sys
import os
from io import StringIO

warnings.filterwarnings('ignore')

# FONCTIONS UTILITAIRES GLOBALES (éliminent les répétitions)
def save_plot(filename, title="", figsize=(8, 6)):
    """Sauvegarde standardisée - élimine 20+ répétitions de plt.tight_layout/savefig"""
    if title:
        plt.title(title)
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()

def create_grid_plot(variables, plot_func, dataset_type, plot_type):
    """Crée grilles de plots - élimine 3 blocs répétitifs de 15+ lignes chacun"""
    if not variables:
        return
    
    n_vars = len(variables)
    if n_vars <= 6:
        n_cols, n_rows, figsize = 3, 2, (15, 8)
    elif n_vars <= 12:
        n_cols, n_rows, figsize = 4, 3, (20, 12)
    else:
        n_cols, n_rows, figsize = 5, (n_vars + 4) // 5, (25, 16)
    
    plt.figure(figsize=figsize)
    for i, var in enumerate(variables):
        if i < n_cols * n_rows:
            plt.subplot(n_rows, n_cols, i+1)
            plot_func(var)
    save_plot(f'{dataset_type}_{plot_type}.png')

def analyze_outliers(data):
    """Analyse outliers - élimine répétition calcul IQR"""
    if len(data) == 0:
        return 0, 0.0
    Q1, Q3 = data.quantile([0.25, 0.75])
    IQR = Q3 - Q1
    outliers = data[(data < Q1 - 1.5*IQR) | (data > Q3 + 1.5*IQR)]
    return len(outliers), (len(outliers) / len(data)) * 100

def get_stats(data):
    """Stats avancées - élimine répétition calculs"""
    if len(data) == 0:
        return None
    q1, q3 = data.quantile([0.25, 0.75])
    return q3 - q1, data.skew(), data.kurtosis()

class CRISPDMAnalyzer:
    """Classe principale pour l'analyse CRISP-DM"""
    
    def __init__(self, dataset_type):
        self.dataset_type = dataset_type
        self.rapport = StringIO()
        self.modeles = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Decision Tree': DecisionTreeClassifier(random_state=42),
            'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
            'KNN': KNeighborsClassifier(n_neighbors=5),
            'SVM': SVC(random_state=42, probability=True),
            'Naive Bayes': GaussianNB(),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42, n_estimators=100)
        }
        
    def log(self, message):
        """Enregistre dans le rapport"""
        self.rapport.write(message + '\n')
        
    def console(self, message):
        """Affiche sur la console"""
        print(message)
        
    def charger_donnees(self):
        """1. COMPRÉHENSION DES DONNÉES"""
        self.console("📊 Phase 1: Chargement et compréhension des données...")
        
        # Chargement et nettoyage
        self.donnees = pd.read_csv(f'./{self.dataset_type}.csv').replace('?', np.nan)
        
        # Optimisation gros datasets
        if len(self.donnees) > 10000:
            self.console(f"⚡ Dataset volumineux détecté ({len(self.donnees)} échantillons)")
            self.console("⚡ Réduction à 10000 échantillons pour accélérer l'analyse...")
            self.donnees = self.donnees.sample(n=10000, random_state=42)
        
        # Variable cible
        self.colonne_cible = 'Approved' if self.dataset_type == 'credit' else self.donnees.columns[-1]
        
        # Identification types variables
        self.vars_numeriques = [col for col in self.donnees.select_dtypes(include=[np.number]).columns 
                               if col != self.colonne_cible]
        self.vars_categorielles = [col for col in self.donnees.select_dtypes(include=['object']).columns 
                                  if col != self.colonne_cible]
        
        # Logging
        self.log(f"=== ANALYSE DU DATASET {self.dataset_type.upper()} ===")
        self.log(f"Dimensions: {self.donnees.shape}")
        self.log(f"Variable cible: {self.colonne_cible}")
        self.log(f"Distribution cible:\n{self.donnees[self.colonne_cible].value_counts()}")
        self.console(f"✅ Dataset {self.dataset_type} chargé: {self.donnees.shape}")
        
    def analyser_donnees(self):
        """2. ANALYSE EXPLORATOIRE (EDA) - 20 points"""
        self.console("📈 Phase 2: Analyse exploratoire des données...")
        
        # Statistiques descriptives
        self.log("\n=== STATISTIQUES DESCRIPTIVES ===")
        self.log(str(self.donnees.describe()))
        
        # Statistiques avancées - REFACTORISÉ
        if self.vars_numeriques:
            self.log(f"\n=== INDICATEURS DE POSITION ET DISPERSION ===")
            for var in self.vars_numeriques:
                data = self.donnees[var].dropna()
                stats = get_stats(data)
                if stats:
                    iqr, skew, kurt = stats
                    self.log(f"{var}: IQR: {iqr:.3f}, Asymétrie: {skew:.3f}, Aplatissement: {kurt:.3f}")
        
        # Valeurs manquantes
        valeurs_manquantes = self.donnees.isnull().sum()
        self.log(f"\n=== VALEURS MANQUANTES ===")
        self.log(str(valeurs_manquantes[valeurs_manquantes > 0]))
        self.log(f"\nVariables numériques: {self.vars_numeriques}")
        self.log(f"Variables catégorielles: {self.vars_categorielles}")
        
        # Outliers - REFACTORISÉ
        self.log(f"\n=== DÉTECTION D'OUTLIERS (méthode IQR) ===")
        self.outliers_info = {}
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0:
                nb_outliers, pourcentage = analyze_outliers(data)
                self.outliers_info[var] = {'nombre': nb_outliers, 'pourcentage': pourcentage}
                self.log(f"{var}: {nb_outliers} outliers ({pourcentage:.1f}%)")
        
        # Visualisations - REFACTORISÉ
        self._creer_visualisations()
        
    def _creer_visualisations(self):
        """Visualisations EDA - MASSIVEMENT REFACTORISÉ"""
        
        # 1. Distribution cible - SIMPLIFIÉ
        plt.figure(figsize=(8, 6))
        self.donnees[self.colonne_cible].value_counts().plot(kind='bar')
        plt.xticks(rotation=0)
        save_plot(f'{self.dataset_type}_target_distribution.png', 
                 f'Distribution de la variable cible - {self.dataset_type.upper()}')
        
        # 2. Corrélations - REFACTORISÉ
        if len(self.vars_numeriques) > 1:
            self._analyser_correlations()
        
        # 3-5. Grilles de plots - REFACTORISÉ (élimine 60+ lignes répétitives)
        create_grid_plot(self.vars_numeriques, self._plot_hist, self.dataset_type, 'histograms')
        create_grid_plot(self.vars_numeriques, self._plot_box, self.dataset_type, 'boxplots')  
        create_grid_plot(self.vars_categorielles, self._plot_count, self.dataset_type, 'countplots')
    
    def _plot_hist(self, var):
        """Plot histogramme"""
        self.donnees[var].hist(bins=30, alpha=0.7)
        plt.title(f'Distribution de {var}')
        plt.xlabel(var)
        plt.ylabel('Fréquence')
    
    def _plot_box(self, var):
        """Plot boxplot"""
        plt.boxplot(self.donnees[var].dropna())
        plt.title(f'Boxplot de {var}')
        plt.ylabel(var)
    
    def _plot_count(self, var):
        """Plot countplot"""
        self.donnees[var].value_counts().head(10).plot(kind='bar')
        plt.title(f'Distribution de {var}')
        plt.xlabel(var)
        plt.ylabel('Fréquence')
        plt.xticks(rotation=45)
    
    def _analyser_correlations(self):
        """Analyse corrélations - REFACTORISÉ"""
        corr_pearson = self.donnees[self.vars_numeriques].corr(method='pearson')
        corr_spearman = self.donnees[self.vars_numeriques].corr(method='spearman')
        
        # Visualisation
        _, axes = plt.subplots(1, 2, figsize=(20, 8))
        sns.heatmap(corr_pearson, annot=True, cmap='coolwarm', center=0, ax=axes[0], fmt='.2f')
        axes[0].set_title(f'Corrélation Pearson - {self.dataset_type.upper()}')
        sns.heatmap(corr_spearman, annot=True, cmap='coolwarm', center=0, ax=axes[1], fmt='.2f')
        axes[1].set_title(f'Corrélation Spearman - {self.dataset_type.upper()}')
        save_plot(f'{self.dataset_type}_correlation_heatmaps.png')
        
        # Comparaison - REFACTORISÉ
        self.log(f"\n=== COMPARAISON CORRÉLATIONS PEARSON vs SPEARMAN ===")
        for i, var1 in enumerate(self.vars_numeriques):
            for j, var2 in enumerate(self.vars_numeriques):
                if i < j:
                    diff = abs(corr_pearson.loc[var1, var2] - corr_spearman.loc[var1, var2])
                    if diff > 0.1:
                        self.log(f"{var1} vs {var2}: Pearson={corr_pearson.loc[var1, var2]:.3f}, "
                                f"Spearman={corr_spearman.loc[var1, var2]:.3f} (diff={diff:.3f})")
        
        # Corrélation avec cible - REFACTORISÉ
        le_temp = LabelEncoder()
        y_encoded = le_temp.fit_transform(self.donnees[self.colonne_cible])
        correlations = [(var, abs(np.corrcoef(self.donnees[var].fillna(0), y_encoded)[0,1])) 
                       for var in self.vars_numeriques]
        correlations.sort(key=lambda x: x[1], reverse=True)
        
        self.log(f"\n=== CORRÉLATIONS AVEC LA CIBLE ===")
        for var, corr in correlations[:5]:
            self.log(f"{var}: {corr:.3f}")

    def preprocesser_donnees(self):
        """3. PRÉPARATION DES DONNÉES - 15 points"""
        self.console("⚙️ Phase 3: Prétraitement des données...")
        self.log(f"\n=== PRÉTRAITEMENT DES DONNÉES ===")

        # Séparation X et y
        X = self.donnees.drop(columns=[self.colonne_cible])
        y = self.donnees[self.colonne_cible]

        # Encodage cible
        self.label_encoder_y = LabelEncoder()
        y_encoded = self.label_encoder_y.fit_transform(y)

        # Imputation - REFACTORISÉ
        self._imputer_donnees(X)

        # Encodage - REFACTORISÉ
        X_encoded = self._encoder_variables(X)

        # Normalisation - REFACTORISÉ
        X_scaled = self._normaliser_donnees(X_encoded)

        # Split et équilibrage
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X_scaled, y_encoded, test_size=0.3, random_state=42, stratify=y_encoded)

        smote = SMOTE(random_state=42)
        self.X_train_balanced, self.y_train_balanced = smote.fit_resample(self.X_train, self.y_train)

        self.log(f"Split 70/30: Train={self.X_train.shape}, Test={self.X_test.shape}")
        self.log(f"SMOTE appliqué: {self.X_train_balanced.shape[0]} échantillons équilibrés")

        # Suggestions feature engineering - REFACTORISÉ
        self._suggerer_feature_engineering()

    def _imputer_donnees(self, X):
        """Imputation intelligente - REFACTORISÉ"""
        # Numériques
        if self.vars_numeriques:
            imputer_num = SimpleImputer(strategy='median')
            X[self.vars_numeriques] = imputer_num.fit_transform(X[self.vars_numeriques])
            self.log(f"Imputation numérique (médiane) pour {len(self.vars_numeriques)} variables")
            self.log("Justification: Médiane choisie car plus robuste aux outliers détectés")

        # Catégorielles
        for var in self.vars_categorielles:
            valeurs_manquantes = X[var].isnull().sum()
            if valeurs_manquantes > 0:
                pourcentage = (valeurs_manquantes / len(X)) * 100
                if pourcentage > 20:
                    X[var] = X[var].fillna('Unknown')
                    self.log(f"Imputation '{var}': 'Unknown' ({pourcentage:.1f}% manquant)")
                else:
                    mode_value = X[var].mode()[0] if not X[var].mode().empty else 'Unknown'
                    X[var] = X[var].fillna(mode_value)
                    self.log(f"Imputation '{var}': mode='{mode_value}' ({pourcentage:.1f}% manquant)")

        if self.vars_categorielles:
            self.log("Justification: 'Unknown' si >20% manquant, sinon mode")

    def _encoder_variables(self, X):
        """Encodage adaptatif - REFACTORISÉ"""
        X_encoded = X.copy()
        variables_ordinales = {
            'education': ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', '10th', '11th', '12th',
                         'HS-grad', 'Some-college', 'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        }

        for var in self.vars_categorielles:
            if var in variables_ordinales:
                # Ordinal
                categories = [cat for cat in variables_ordinales[var] if cat in X[var].unique()]
                if len(categories) > 1:
                    ordinal_encoder = OrdinalEncoder(categories=[categories], handle_unknown='use_encoded_value', unknown_value=-1)
                    X_encoded[var] = ordinal_encoder.fit_transform(X[[var]]).flatten()
                    self.log(f"Ordinal Encoding pour {var}: {len(categories)} niveaux ordonnés")
                else:
                    le = LabelEncoder()
                    X_encoded[var] = le.fit_transform(X[var].astype(str))
                    self.log(f"Label Encoding pour {var} (fallback)")
            elif X[var].nunique() <= 10:
                # One-hot
                dummies = pd.get_dummies(X[var], prefix=var, drop_first=True)
                X_encoded = pd.concat([X_encoded.drop(columns=[var]), dummies], axis=1)
                self.log(f"One-Hot Encoding pour {var}: {dummies.shape[1]} nouvelles variables")
            else:
                # Label
                le = LabelEncoder()
                X_encoded[var] = le.fit_transform(X[var].astype(str))
                self.log(f"Label Encoding pour {var}")

        return X_encoded

    def _normaliser_donnees(self, X_encoded):
        """Normalisation intelligente - REFACTORISÉ"""
        has_outliers = hasattr(self, 'outliers_info') and any(
            info['pourcentage'] > 10 for info in self.outliers_info.values())

        if has_outliers:
            scaler = MinMaxScaler()
            scaler_name = "MinMaxScaler"
            justification = "MinMaxScaler choisi car présence d'outliers significatifs"
        else:
            scaler = StandardScaler()
            scaler_name = "StandardScaler"
            justification = "StandardScaler choisi car distribution relativement normale"

        X_scaled = scaler.fit_transform(X_encoded)
        self.log(f"{scaler_name} appliqué à {X_scaled.shape[1]} variables")
        self.log(f"Justification: {justification}")
        return X_scaled

    def _suggerer_feature_engineering(self):
        """Suggestions FE - REFACTORISÉ"""
        self.log(f"\n=== SUGGESTIONS DE FEATURE ENGINEERING ===")
        suggestions = []

        # Asymétrie
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0 and abs(data.skew()) > 1:
                suggestions.append(f"Transformation log pour '{var}' (asymétrie: {data.skew():.2f})")

        # Outliers
        if hasattr(self, 'outliers_info'):
            for var, info in self.outliers_info.items():
                if info['pourcentage'] > 5:
                    suggestions.append(f"Traitement outliers pour '{var}' ({info['pourcentage']:.1f}%)")

        # Modalités
        for var in self.vars_categorielles:
            if self.donnees[var].nunique() > 20:
                suggestions.append(f"Regroupement de catégories pour '{var}' ({self.donnees[var].nunique()} modalités)")

        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                self.log(f"{i}. {suggestion}")
        else:
            self.log("Aucune suggestion spécifique identifiée")

    def modeliser(self):
        """4. MODÉLISATION - 25 points"""
        self.console("🤖 Phase 4: Modélisation avec 7 algorithmes...")
        self.log(f"\n=== ÉVALUATION DES 7 ALGORITHMES ===")
        self.resultats = {}

        for nom, modele in self.modeles.items():
            try:
                # Entraînement et prédictions
                modele.fit(self.X_train_balanced, self.y_train_balanced)
                y_train_pred = modele.predict(self.X_train_balanced)
                y_test_pred = modele.predict(self.X_test)

                # Métriques - REFACTORISÉ
                metrics = self._calculer_metriques(modele, y_train_pred, y_test_pred)
                self.resultats[nom] = metrics

                self.log(f"{nom:20} | F1: {metrics['f1_score']:.4f} | Acc: {metrics['test_accuracy']:.4f} | Over: {metrics['overfitting']:.4f}")

            except Exception as e:
                self.log(f"{nom:20} | ERREUR: {str(e)}")

    def _calculer_metriques(self, modele, y_train_pred, y_test_pred):
        """Calcul métriques - REFACTORISÉ"""
        train_acc = accuracy_score(self.y_train_balanced, y_train_pred)
        test_acc = accuracy_score(self.y_test, y_test_pred)

        # AUC-ROC
        try:
            y_proba = modele.predict_proba(self.X_test)[:, 1]
            auc = roc_auc_score(self.y_test, y_proba)
        except:
            auc = 0

        return {
            'train_accuracy': train_acc,
            'test_accuracy': test_acc,
            'precision': precision_score(self.y_test, y_test_pred, average='weighted'),
            'recall': recall_score(self.y_test, y_test_pred, average='weighted'),
            'f1_score': f1_score(self.y_test, y_test_pred, average='weighted'),
            'auc_roc': auc,
            'overfitting': train_acc - test_acc
        }

    def valider(self):
        """5. VALIDATION ET COMPARAISON - 15 points"""
        self.console("📊 Phase 5: Validation croisée...")
        self.log(f"\n=== VALIDATION CROISÉE ===")

        # Top 3 modèles
        df_resultats = pd.DataFrame(self.resultats).T
        top3 = df_resultats.nlargest(3, 'f1_score')

        for fold in [5, 7, 10]:
            self.log(f"\n--- Validation croisée {fold}-folds ---")
            cv = StratifiedKFold(n_splits=fold, shuffle=True, random_state=42)

            for nom in top3.index:
                scores = cross_val_score(self.modeles[nom], self.X_train_balanced, self.y_train_balanced,
                                       cv=cv, scoring='f1_weighted')
                self.log(f"{nom:20} | F1-CV: {scores.mean():.4f} (±{scores.std():.4f})")

    def evaluer(self):
        """6. ÉVALUATION ET ANALYSE - 10 points"""
        self.console("📈 Phase 6: Évaluation et analyse des résultats...")

        # Tableau comparatif
        df_resultats = pd.DataFrame(self.resultats).T.sort_values('f1_score', ascending=False)

        self.log(f"\n=== TABLEAU COMPARATIF DES RÉSULTATS ===")
        self.log(f"{'Modèle':<20} {'F1':<8} {'Accuracy':<8} {'AUC':<8} {'Overfitting':<12}")
        self.log("-" * 60)

        for nom, row in df_resultats.iterrows():
            self.log(f"{nom:<20} {row['f1_score']:<8.4f} {row['test_accuracy']:<8.4f} "
                    f"{row['auc_roc']:<8.4f} {row['overfitting']:<12.4f}")

        # Meilleur modèle
        self.meilleur_modele = df_resultats.index[0]
        self.log(f"\n=== MEILLEUR MODÈLE ===")
        self.log(f"Modèle sélectionné: {self.meilleur_modele}")
        self.log(f"Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")

        # Visualisations finales - REFACTORISÉ
        self._creer_visualisations_finales(df_resultats)

        # Analyse overfitting
        self._analyser_overfitting(df_resultats)

    def _creer_visualisations_finales(self, df_resultats):
        """Visualisations finales - REFACTORISÉ"""
        modele_final = self.modeles[self.meilleur_modele]
        modele_final.fit(self.X_train_balanced, self.y_train_balanced)
        y_pred_final = modele_final.predict(self.X_test)

        # Matrice de confusion
        plt.figure(figsize=(8, 6))
        cm = confusion_matrix(self.y_test, y_pred_final)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.ylabel('Réalité')
        plt.xlabel('Prédiction')
        save_plot(f'{self.dataset_type}_confusion_matrix.png', f'Matrice de confusion - {self.meilleur_modele}')

        # Courbe ROC
        if df_resultats.loc[self.meilleur_modele, 'auc_roc'] > 0:
            y_proba = modele_final.predict_proba(self.X_test)[:, 1]
            fpr, tpr, _ = roc_curve(self.y_test, y_proba)

            plt.figure(figsize=(8, 6))
            plt.plot(fpr, tpr, linewidth=2,
                    label=f'{self.meilleur_modele} (AUC = {df_resultats.loc[self.meilleur_modele, "auc_roc"]:.3f})')
            plt.plot([0, 1], [0, 1], 'k--', alpha=0.6, label='Classification aléatoire')
            plt.xlabel('Taux de Faux Positifs')
            plt.ylabel('Taux de Vrais Positifs')
            plt.legend()
            plt.grid(True, alpha=0.3)
            save_plot(f'{self.dataset_type}_roc_curve.png', f'Courbe ROC - {self.dataset_type.upper()}')

        # Classification report
        self.log(f"\n=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===")
        self.log(f"Modèle: {self.meilleur_modele}")
        self.log(f"\n{classification_report(self.y_test, y_pred_final)}")

    def _analyser_overfitting(self, df_resultats):
        """Analyse overfitting - REFACTORISÉ"""
        self.log(f"\n=== ANALYSE DE L'OVERFITTING ===")
        overfitting_moyen = df_resultats['overfitting'].mean()
        self.log(f"Overfitting moyen: {overfitting_moyen:.4f}")

        if overfitting_moyen > 0.1:
            self.log("⚠️ Overfitting détecté - Recommandations:")
            self.log("- Réduire la complexité des modèles")
            self.log("- Augmenter la régularisation")
            self.log("- Collecter plus de données")
        else:
            self.log("✅ Pas d'overfitting significatif détecté")

    def generer_rapport(self):
        """7. GÉNÉRATION DU RAPPORT FINAL"""
        self.console("📄 Phase 7: Génération du rapport...")

        # Recommandations
        df_resultats = pd.DataFrame(self.resultats).T

        self.log(f"\n=== RECOMMANDATIONS ===")
        self.log(f"1. MODÈLE RECOMMANDÉ: {self.meilleur_modele}")
        self.log(f"   - Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")
        self.log(f"   - Accuracy: {df_resultats.loc[self.meilleur_modele, 'test_accuracy']:.4f}")

        self.log(f"\n2. POINTS CLÉS:")
        self.log(f"   - {len(self.modeles)} algorithmes évalués")
        self.log(f"   - Split 70/30 avec validation croisée")
        self.log(f"   - Équilibrage SMOTE appliqué")
        self.log(f"   - Prétraitement complet réalisé")

        self.log(f"\n3. AMÉLIORATIONS POSSIBLES:")
        self.log(f"   - Optimisation des hyperparamètres")
        self.log(f"   - Feature engineering avancé")
        self.log(f"   - Ensemble methods")

        # Sauvegarde rapport
        nom_rapport = f'RAPPORT_ANALYSE_{self.dataset_type.upper()}.txt'
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write(f"RAPPORT D'ANALYSE CRISP-DM - {self.dataset_type.upper()}\n")
            f.write("=" * 60 + "\n")
            f.write(f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(self.rapport.getvalue())

        self.console(f"✅ Analyse terminée!")
        self.console(f"📊 Meilleur modèle: {self.meilleur_modele}")
        self.console(f"📁 Rapport: {nom_rapport}")
        self.console(f"📈 Visualisations: 4 graphiques générés")

    def executer_analyse_complete(self):
        """Exécute l'analyse CRISP-DM complète"""
        self.console("🚀 Démarrage de l'analyse CRISP-DM...")

        try:
            self.charger_donnees()
            self.analyser_donnees()
            self.preprocesser_donnees()
            self.modeliser()
            self.valider()
            self.evaluer()
            self.generer_rapport()

        except Exception as e:
            self.console(f"❌ Erreur: {str(e)}")
            raise

def main():
    """Fonction principale"""
    # Nettoyage des anciens fichiers
    os.system('rm -f *.png *RAPPORT*.txt')

    # Sélection du dataset
    if len(sys.argv) > 1:
        dataset = sys.argv[1].lower()
    else:
        print("Datasets disponibles:")
        print("1. credit")
        print("2. revenu")
        dataset = input("Choisissez (credit/revenu): ").lower()

    if dataset not in ['credit', 'revenu']:
        print("❌ Dataset non reconnu. Utilisation de 'credit' par défaut.")
        dataset = 'credit'

    # Exécution de l'analyse
    analyzer = CRISPDMAnalyzer(dataset)
    analyzer.executer_analyse_complete()

if __name__ == "__main__":
    main()
