#!/usr/bin/env python3
"""
INF5082 - TP1 : Analyse Exploratoire et Modélisation de Données
Méthodologie CRISP-DM - Version Optimisée

Auteur: [Votre nom]
Date: 2025-06-21
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from imblearn.over_sampling import SMOTE
import warnings
import sys
import os
from io import StringIO

warnings.filterwarnings('ignore')

class CRISPDMAnalyzer:
    """Classe principale pour l'analyse CRISP-DM"""
    
    def __init__(self, dataset_type):
        self.dataset_type = dataset_type
        self.rapport = StringIO()
        self.modeles = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Decision Tree': DecisionTreeClassifier(random_state=42),
            'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
            'KNN': KNeighborsClassifier(n_neighbors=5),
            'SVM': SVC(random_state=42, probability=True),
            'Naive Bayes': GaussianNB(),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42, n_estimators=100)
        }
        
    def log(self, message):
        """Enregistre dans le rapport"""
        self.rapport.write(message + '\n')
        
    def console(self, message):
        """Affiche sur la console"""
        print(message)
        
    def charger_donnees(self):
        """1. COMPRÉHENSION DES DONNÉES"""
        self.console("📊 Phase 1: Chargement et compréhension des données...")
        
        fichier = f'./{self.dataset_type}.csv'
        self.donnees = pd.read_csv(fichier)
        
        # Nettoyage des valeurs manquantes
        self.donnees = self.donnees.replace('?', np.nan)

        # Optimisation pour gros datasets
        if len(self.donnees) > 10000:
            self.console(f"⚡ Dataset volumineux détecté ({len(self.donnees)} échantillons)")
            self.console("⚡ Réduction à 10000 échantillons pour accélérer l'analyse...")
            self.donnees = self.donnees.sample(n=10000, random_state=42)
        
        # Détection automatique de la variable cible
        if self.dataset_type == 'credit':
            self.colonne_cible = 'Approved'
        else:  # revenu
            self.colonne_cible = self.donnees.columns[-1]  # Dernière colonne
            
        self.log(f"=== ANALYSE DU DATASET {self.dataset_type.upper()} ===")
        self.log(f"Dimensions: {self.donnees.shape}")
        self.log(f"Variable cible: {self.colonne_cible}")
        self.log(f"Distribution cible:\n{self.donnees[self.colonne_cible].value_counts()}")
        
        self.console(f"✅ Dataset {self.dataset_type} chargé: {self.donnees.shape}")
        
    def analyser_donnees(self):
        """2. ANALYSE EXPLORATOIRE (EDA) - 20 points"""
        self.console("📈 Phase 2: Analyse exploratoire des données...")
        
        # Variables numériques et catégorielles (d'abord identifier les types)
        self.vars_numeriques = self.donnees.select_dtypes(include=[np.number]).columns.tolist()
        if self.colonne_cible in self.vars_numeriques:
            self.vars_numeriques.remove(self.colonne_cible)

        self.vars_categorielles = self.donnees.select_dtypes(include=['object']).columns.tolist()
        if self.colonne_cible in self.vars_categorielles:
            self.vars_categorielles.remove(self.colonne_cible)

        # Statistiques descriptives
        self.log("\n=== STATISTIQUES DESCRIPTIVES ===")
        self.log(str(self.donnees.describe()))

        # Statistiques avancées (position et dispersion)
        if self.vars_numeriques:
            self.log(f"\n=== INDICATEURS DE POSITION ET DISPERSION ===")
            for var in self.vars_numeriques:
                data = self.donnees[var].dropna()
                if len(data) > 0:
                    q1, q3 = data.quantile([0.25, 0.75])
                    iqr = q3 - q1
                    asymetrie = data.skew()
                    aplatissement = data.kurtosis()
                    self.log(f"{var}:")
                    self.log(f"  IQR: {iqr:.3f}, Asymétrie: {asymetrie:.3f}, Aplatissement: {aplatissement:.3f}")

        # Valeurs manquantes
        valeurs_manquantes = self.donnees.isnull().sum()
        self.log(f"\n=== VALEURS MANQUANTES ===")
        self.log(str(valeurs_manquantes[valeurs_manquantes > 0]))
        
        self.log(f"\nVariables numériques: {self.vars_numeriques}")
        self.log(f"Variables catégorielles: {self.vars_categorielles}")
        
        # Détection d'outliers (méthode IQR)
        self.log(f"\n=== DÉTECTION D'OUTLIERS (méthode IQR) ===")
        self.outliers_info = {}
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0:
                Q1, Q3 = data.quantile([0.25, 0.75])
                IQR = Q3 - Q1
                limite_inf = Q1 - 1.5 * IQR
                limite_sup = Q3 + 1.5 * IQR
                outliers = data[(data < limite_inf) | (data > limite_sup)]
                pourcentage = (len(outliers) / len(data)) * 100
                self.outliers_info[var] = {'nombre': len(outliers), 'pourcentage': pourcentage}
                self.log(f"{var}: {len(outliers)} outliers ({pourcentage:.1f}%)")

        # Visualisations essentielles
        self._creer_visualisations_eda()
        
    def _creer_visualisations_eda(self):
        """Crée les visualisations essentielles pour l'EDA"""
        
        # 1. Distribution de la variable cible
        plt.figure(figsize=(8, 6))
        self.donnees[self.colonne_cible].value_counts().plot(kind='bar')
        plt.title(f'Distribution de la variable cible - {self.dataset_type.upper()}')
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig(f'{self.dataset_type}_target_distribution.png', dpi=300)
        plt.close()
        
        # 2. Matrice de corrélation
        if len(self.vars_numeriques) > 1:
            plt.figure(figsize=(10, 8))
            corr_matrix = self.donnees[self.vars_numeriques].corr()
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
            plt.title(f'Matrice de corrélation - {self.dataset_type.upper()}')
            plt.tight_layout()
            plt.savefig(f'{self.dataset_type}_correlation.png', dpi=300)
            plt.close()
            
            # Analyse des corrélations avec la cible
            if self.colonne_cible in self.donnees.columns:
                # Encoder temporairement la cible pour la corrélation
                le_temp = LabelEncoder()
                y_encoded = le_temp.fit_transform(self.donnees[self.colonne_cible])
                correlations = []
                for var in self.vars_numeriques:
                    corr = np.corrcoef(self.donnees[var].fillna(0), y_encoded)[0,1]
                    correlations.append((var, abs(corr)))
                
                correlations.sort(key=lambda x: x[1], reverse=True)
                self.log(f"\n=== CORRÉLATIONS AVEC LA CIBLE ===")
                for var, corr in correlations[:5]:
                    self.log(f"{var}: {corr:.3f}")
        
        # 3. Histogrammes des variables numériques
        if self.vars_numeriques:
            n_vars = len(self.vars_numeriques)
            n_cols = min(3, n_vars)
            n_rows = (n_vars + n_cols - 1) // n_cols
            
            plt.figure(figsize=(15, 5*n_rows))
            for i, var in enumerate(self.vars_numeriques):
                plt.subplot(n_rows, n_cols, i+1)
                self.donnees[var].hist(bins=30, alpha=0.7)
                plt.title(f'Distribution de {var}')
                plt.xlabel(var)
                plt.ylabel('Fréquence')
            plt.tight_layout()
            plt.savefig(f'{self.dataset_type}_histograms.png', dpi=300)
            plt.close()

        # 4. Boxplots pour détecter les outliers visuellement
        if self.vars_numeriques and len(self.vars_numeriques) <= 6:  # Limite pour lisibilité
            plt.figure(figsize=(15, 8))
            for i, var in enumerate(self.vars_numeriques):
                plt.subplot(2, 3, i+1)
                plt.boxplot(self.donnees[var].dropna())
                plt.title(f'Boxplot de {var}')
                plt.ylabel(var)
            plt.tight_layout()
            plt.savefig(f'{self.dataset_type}_boxplots.png', dpi=300)
            plt.close()
            
    def preprocesser_donnees(self):
        """3. PRÉPARATION DES DONNÉES - 15 points"""
        self.console("⚙️ Phase 3: Prétraitement des données...")
        
        self.log(f"\n=== PRÉTRAITEMENT DES DONNÉES ===")
        
        # Séparation X et y
        X = self.donnees.drop(columns=[self.colonne_cible])
        y = self.donnees[self.colonne_cible]
        
        # Encodage de la variable cible
        self.label_encoder_y = LabelEncoder()
        y_encoded = self.label_encoder_y.fit_transform(y)
        
        # Imputation des valeurs manquantes
        # Variables numériques
        if self.vars_numeriques:
            imputer_num = SimpleImputer(strategy='median')
            X[self.vars_numeriques] = imputer_num.fit_transform(X[self.vars_numeriques])
            self.log(f"Imputation numérique (médiane) pour {len(self.vars_numeriques)} variables")
        
        # Variables catégorielles
        if self.vars_categorielles:
            imputer_cat = SimpleImputer(strategy='most_frequent')
            X[self.vars_categorielles] = imputer_cat.fit_transform(X[self.vars_categorielles])
            self.log(f"Imputation catégorielle (mode) pour {len(self.vars_categorielles)} variables")
        
        # Encodage des variables catégorielles
        X_encoded = X.copy()
        for var in self.vars_categorielles:
            if X[var].nunique() <= 10:  # One-hot pour peu de modalités
                dummies = pd.get_dummies(X[var], prefix=var, drop_first=True)
                X_encoded = pd.concat([X_encoded.drop(columns=[var]), dummies], axis=1)
                self.log(f"One-Hot Encoding pour {var}: {dummies.shape[1]} nouvelles variables")
            else:  # Label encoding pour beaucoup de modalités
                le = LabelEncoder()
                X_encoded[var] = le.fit_transform(X[var].astype(str))
                self.log(f"Label Encoding pour {var}")
        
        # Normalisation
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        self.log(f"StandardScaler appliqué à {X_scaled.shape[1]} variables")
        
        # Split 70/30
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X_scaled, y_encoded, test_size=0.3, random_state=42, stratify=y_encoded
        )
        
        self.log(f"Split 70/30: Train={self.X_train.shape}, Test={self.X_test.shape}")
        
        # Équilibrage avec SMOTE
        smote = SMOTE(random_state=42)
        self.X_train_balanced, self.y_train_balanced = smote.fit_resample(self.X_train, self.y_train)
        
        self.log(f"SMOTE appliqué: {self.X_train_balanced.shape[0]} échantillons équilibrés")

        # Suggestions de feature engineering
        self.log(f"\n=== SUGGESTIONS DE FEATURE ENGINEERING ===")
        suggestions = []

        # Basé sur l'asymétrie des variables
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0 and abs(data.skew()) > 1:
                suggestions.append(f"Transformation log pour '{var}' (asymétrie: {data.skew():.2f})")

        # Basé sur les outliers détectés
        if hasattr(self, 'outliers_info'):
            for var, info in self.outliers_info.items():
                if info['pourcentage'] > 5:
                    suggestions.append(f"Traitement outliers pour '{var}' ({info['pourcentage']:.1f}%)")

        # Variables catégorielles avec trop de modalités
        for var in self.vars_categorielles:
            if self.donnees[var].nunique() > 20:
                suggestions.append(f"Regroupement de catégories pour '{var}' ({self.donnees[var].nunique()} modalités)")

        # Affichage des suggestions
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                self.log(f"{i}. {suggestion}")
        else:
            self.log("Aucune suggestion spécifique identifiée")
        
    def modeliser(self):
        """4. MODÉLISATION - 25 points"""
        self.console("🤖 Phase 4: Modélisation avec 7 algorithmes...")
        
        self.resultats = {}
        
        self.log(f"\n=== ÉVALUATION DES 7 ALGORITHMES ===")
        
        for nom, modele in self.modeles.items():
            try:
                # Entraînement
                modele.fit(self.X_train_balanced, self.y_train_balanced)
                
                # Prédictions
                y_train_pred = modele.predict(self.X_train_balanced)
                y_test_pred = modele.predict(self.X_test)
                
                # Métriques
                train_acc = accuracy_score(self.y_train_balanced, y_train_pred)
                test_acc = accuracy_score(self.y_test, y_test_pred)
                precision = precision_score(self.y_test, y_test_pred, average='weighted')
                recall = recall_score(self.y_test, y_test_pred, average='weighted')
                f1 = f1_score(self.y_test, y_test_pred, average='weighted')
                
                # AUC-ROC
                try:
                    y_proba = modele.predict_proba(self.X_test)[:, 1]
                    auc = roc_auc_score(self.y_test, y_proba)
                except:
                    auc = 0
                
                # Overfitting
                overfitting = train_acc - test_acc
                
                self.resultats[nom] = {
                    'train_accuracy': train_acc,
                    'test_accuracy': test_acc,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc_roc': auc,
                    'overfitting': overfitting
                }
                
                self.log(f"{nom:20} | F1: {f1:.4f} | Acc: {test_acc:.4f} | Over: {overfitting:.4f}")
                
            except Exception as e:
                self.log(f"{nom:20} | ERREUR: {str(e)}")
                
    def valider(self):
        """5. VALIDATION ET COMPARAISON - 15 points"""
        self.console("📊 Phase 5: Validation croisée...")
        
        self.log(f"\n=== VALIDATION CROISÉE ===")
        
        # Validation croisée pour les 3 meilleurs modèles
        df_resultats = pd.DataFrame(self.resultats).T
        top3 = df_resultats.nlargest(3, 'f1_score')
        
        for fold in [5, 7, 10]:
            self.log(f"\n--- Validation croisée {fold}-folds ---")
            cv = StratifiedKFold(n_splits=fold, shuffle=True, random_state=42)
            
            for nom in top3.index:
                modele = self.modeles[nom]
                scores = cross_val_score(modele, self.X_train_balanced, self.y_train_balanced, 
                                       cv=cv, scoring='f1_weighted')
                self.log(f"{nom:20} | F1-CV: {scores.mean():.4f} (±{scores.std():.4f})")

    def evaluer(self):
        """6. ÉVALUATION ET ANALYSE - 10 points"""
        self.console("📈 Phase 6: Évaluation et analyse des résultats...")

        # Tableau comparatif
        df_resultats = pd.DataFrame(self.resultats).T
        df_resultats = df_resultats.sort_values('f1_score', ascending=False)

        self.log(f"\n=== TABLEAU COMPARATIF DES RÉSULTATS ===")
        self.log(f"{'Modèle':<20} {'F1':<8} {'Accuracy':<8} {'AUC':<8} {'Overfitting':<12}")
        self.log("-" * 60)

        for nom, row in df_resultats.iterrows():
            self.log(f"{nom:<20} {row['f1_score']:<8.4f} {row['test_accuracy']:<8.4f} "
                    f"{row['auc_roc']:<8.4f} {row['overfitting']:<12.4f}")

        # Meilleur modèle
        self.meilleur_modele = df_resultats.index[0]
        self.log(f"\n=== MEILLEUR MODÈLE ===")
        self.log(f"Modèle sélectionné: {self.meilleur_modele}")
        self.log(f"Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")

        # Matrice de confusion du meilleur modèle
        modele_final = self.modeles[self.meilleur_modele]
        modele_final.fit(self.X_train_balanced, self.y_train_balanced)
        y_pred_final = modele_final.predict(self.X_test)

        # Visualisation de la matrice de confusion
        plt.figure(figsize=(8, 6))
        cm = confusion_matrix(self.y_test, y_pred_final)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'Matrice de confusion - {self.meilleur_modele}')
        plt.ylabel('Réalité')
        plt.xlabel('Prédiction')
        plt.tight_layout()
        plt.savefig(f'{self.dataset_type}_confusion_matrix.png', dpi=300)
        plt.close()

        # Classification report
        self.log(f"\n=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===")
        self.log(f"Modèle: {self.meilleur_modele}")
        self.log(f"\n{classification_report(self.y_test, y_pred_final)}")

        # Analyse de l'overfitting
        self.log(f"\n=== ANALYSE DE L'OVERFITTING ===")
        overfitting_moyen = df_resultats['overfitting'].mean()
        self.log(f"Overfitting moyen: {overfitting_moyen:.4f}")

        if overfitting_moyen > 0.1:
            self.log("⚠️ Overfitting détecté - Recommandations:")
            self.log("- Réduire la complexité des modèles")
            self.log("- Augmenter la régularisation")
            self.log("- Collecter plus de données")
        else:
            self.log("✅ Pas d'overfitting significatif détecté")

        # Courbe ROC pour le meilleur modèle
        if df_resultats.loc[self.meilleur_modele, 'auc_roc'] > 0:
            from sklearn.metrics import roc_curve
            y_proba = modele_final.predict_proba(self.X_test)[:, 1]
            fpr, tpr, _ = roc_curve(self.y_test, y_proba)

            plt.figure(figsize=(8, 6))
            plt.plot(fpr, tpr, linewidth=2,
                    label=f'{self.meilleur_modele} (AUC = {df_resultats.loc[self.meilleur_modele, "auc_roc"]:.3f})')
            plt.plot([0, 1], [0, 1], 'k--', alpha=0.6, label='Classification aléatoire')
            plt.xlabel('Taux de Faux Positifs')
            plt.ylabel('Taux de Vrais Positifs')
            plt.title(f'Courbe ROC - {self.dataset_type.upper()}')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(f'{self.dataset_type}_roc_curve.png', dpi=300)
            plt.close()

    def generer_rapport(self):
        """7. GÉNÉRATION DU RAPPORT FINAL"""
        self.console("📄 Phase 7: Génération du rapport...")

        # Recommandations
        df_resultats = pd.DataFrame(self.resultats).T

        self.log(f"\n=== RECOMMANDATIONS ===")
        self.log(f"1. MODÈLE RECOMMANDÉ: {self.meilleur_modele}")
        self.log(f"   - Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")
        self.log(f"   - Accuracy: {df_resultats.loc[self.meilleur_modele, 'test_accuracy']:.4f}")

        self.log(f"\n2. POINTS CLÉS:")
        self.log(f"   - {len(self.modeles)} algorithmes évalués")
        self.log(f"   - Split 70/30 avec validation croisée")
        self.log(f"   - Équilibrage SMOTE appliqué")
        self.log(f"   - Prétraitement complet réalisé")

        self.log(f"\n3. AMÉLIORATIONS POSSIBLES:")
        self.log(f"   - Optimisation des hyperparamètres")
        self.log(f"   - Feature engineering avancé")
        self.log(f"   - Ensemble methods")

        # Sauvegarde du rapport
        nom_rapport = f'RAPPORT_ANALYSE_{self.dataset_type.upper()}.txt'
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write(f"RAPPORT D'ANALYSE CRISP-DM - {self.dataset_type.upper()}\n")
            f.write("=" * 60 + "\n")
            f.write(f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(self.rapport.getvalue())

        self.console(f"✅ Analyse terminée!")
        self.console(f"📊 Meilleur modèle: {self.meilleur_modele}")
        self.console(f"📁 Rapport: {nom_rapport}")
        self.console(f"📈 Visualisations: 4 graphiques générés")

    def executer_analyse_complete(self):
        """Exécute l'analyse CRISP-DM complète"""
        self.console("🚀 Démarrage de l'analyse CRISP-DM...")

        try:
            self.charger_donnees()
            self.analyser_donnees()
            self.preprocesser_donnees()
            self.modeliser()
            self.valider()
            self.evaluer()
            self.generer_rapport()

        except Exception as e:
            self.console(f"❌ Erreur: {str(e)}")
            raise

def main():
    """Fonction principale"""
    # Nettoyage des anciens fichiers
    for fichier in ['*.png', '*RAPPORT*.txt']:
        os.system(f'rm -f {fichier}')

    # Sélection du dataset
    if len(sys.argv) > 1:
        dataset = sys.argv[1].lower()
    else:
        print("Datasets disponibles:")
        print("1. credit")
        print("2. revenu")
        dataset = input("Choisissez (credit/revenu): ").lower()

    if dataset not in ['credit', 'revenu']:
        print("❌ Dataset non reconnu. Utilisation de 'credit' par défaut.")
        dataset = 'credit'

    # Exécution de l'analyse
    analyzer = CRISPDMAnalyzer(dataset)
    analyzer.executer_analyse_complete()

if __name__ == "__main__":
    main()
