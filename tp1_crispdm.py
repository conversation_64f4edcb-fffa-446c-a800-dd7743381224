#%% 1. IMPORTATION DES BIBLIOTHÈQUES
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import os
import warnings
import time
from datetime import datetime
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, cross_validate, StratifiedKFold
from sklearn.preprocessing import StandardScaler, OneHotEncoder, OrdinalEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                            f1_score, roc_auc_score, confusion_matrix, 
                            classification_report, RocCurveDisplay)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC, LinearSVC
from sklearn.naive_bayes import GaussianNB
from imblearn.over_sampling import SMOTE, RandomOverSampler
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import make_pipeline

#%% 2. CHARGEMENT ET PRÉTRAITEMENT DES DONNÉES
def load_and_preprocess(dataset_name):
    """Charge et prétraite les données selon le dataset spécifié"""
    if dataset_name == "Revenu":
        # Chargement des données Revenu
        revenu_columns = [
            'age', 'workclass', 'fnlwgt', 'education', 'education-num', 
            'marital-status', 'occupation', 'relationship', 'race', 
            'sex', 'capital-gain', 'capital-loss', 'hours-per-week', 
            'native-country', 'income'
        ]
        
        # Vérifier si le fichier existe
        if not os.path.exists("revenu.csv"):
            raise FileNotFoundError("Fichier revenu.csv introuvable. Placez-le dans le même répertoire que ce script.")
        
        # Charger seulement 10 000 lignes pour les tests initiaux
        df = pd.read_csv("revenu.csv", names=revenu_columns, na_values="?", skipinitialspace=True)
        
        # Conversion des colonnes numériques
        numeric_cols = ['age', 'fnlwgt', 'education-num', 'capital-gain', 'capital-loss', 'hours-per-week']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Nettoyage des colonnes catégorielles
        categorical_cols = ['workclass', 'education', 'marital-status', 'occupation', 
                            'relationship', 'race', 'sex', 'native-country', 'income']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
        
        # Séparation features/cible
        X = df.drop('income', axis=1)
        y = df['income'].apply(lambda x: 1 if x.strip() == '>50K' else 0)
        
        # Vérification de la distribution des classes
        class_counts = y.value_counts()
        if len(class_counts) < 2:
            raise ValueError(f"La variable cible ne contient qu'une seule classe: {class_counts.index[0]}")
        
        # Définition des types de variables
        num_features = numeric_cols
        cat_nominal = ['workclass', 'occupation', 'relationship', 'race', 'sex', 'native-country']
        cat_ordinal = ['education', 'marital-status']
        
        # Ordres pour les variables ordinales
        education_order = ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', 
                           '10th', '11th', '12th', 'HS-grad', 'Some-college', 
                           'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        marital_order = ['Never-married', 'Married-spouse-absent', 'Separated', 
                         'Divorced', 'Widowed', 'Married-civ-spouse', 'Married-AF-spouse']
        
        # Pour l'OrdinalEncoder, nous avons deux colonnes, donc nous avons besoin de deux listes d'ordres
        ordinal_categories = [education_order, marital_order]
        
    elif dataset_name == "Crédit":
        # Vérifier si le fichier existe
        if not os.path.exists("credit.csv"):
            raise FileNotFoundError("Fichier credit.csv introuvable. Placez-le dans le même répertoire que ce script.")

        # Charger le fichier avec les en-têtes existants
        df = pd.read_csv("credit.csv", na_values="?", skipinitialspace=True)
        
        # Conversion des colonnes numériques (selon les vraies colonnes du fichier)
        numeric_cols = ['Age', 'Debt', 'YearsEmployed', 'CreditScore', 'Income']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Nettoyage des colonnes catégorielles
        categorical_cols = ['Gender', 'Married', 'BankCustomer', 'Industry', 'Ethnicity',
                           'PriorDefault', 'Employed', 'DriversLicense', 'Citizen', 'ZipCode', 'Approved']
        for col in categorical_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()

        # Séparation features/cible
        X = df.drop('Approved', axis=1)

        # Vérification et création de la variable cible
        if 'Approved' not in df.columns:
            raise KeyError("La colonne 'Approved' est introuvable dans le dataset Crédit")

        # Création de la variable cible
        y = df['Approved'].apply(lambda x: 1 if str(x).strip() in ['1', '1.0'] else 0)
        
        # Vérification de la distribution des classes
        class_counts = y.value_counts()
        if len(class_counts) < 2:
            raise ValueError(f"La variable cible ne contient qu'une seule classe: {class_counts.index[0]}")
        
        # Définition des types de variables
        num_features = numeric_cols
        cat_nominal = ['A1', 'A4', 'A6', 'A7', 'A9', 'A10', 'A12', 'A13']
        cat_ordinal = []  # Aucune variable ordinale dans ce dataset
        
        # Pas d'ordres pour les variables ordinales
        ordinal_categories = []
    
    # Préprocesseur avec gestion des valeurs inconnues
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='median')),
                ('scaler', StandardScaler())]), num_features),
            ('nom', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='most_frequent')),
                ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))]), cat_nominal),
            ('ord', Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='most_frequent')),
                ('encoder', OrdinalEncoder(categories=ordinal_categories, handle_unknown='use_encoded_value', unknown_value=-1))
            ]), cat_ordinal)
        ], remainder='drop')
    
    return X, y, preprocessor, num_features, cat_nominal, cat_ordinal

#%% 3. ANALYSE EXPLORATOIRE (EDA)
def perform_eda(X, y, num_features, cat_nominal, cat_ordinal, dataset_name):
    """Réalise l'analyse exploratoire des données"""
    print(f"\n{'='*50}")
    print(f"ANALYSE EXPLORATOIRE - DATASET {dataset_name}")
    print(f"{'='*50}\n")
    
    # Création d'un DataFrame temporaire pour l'EDA
    df_eda = X.copy()
    df_eda['Target'] = y
    
    # Nettoyage des colonnes catégorielles
    categorical_cols = cat_nominal + cat_ordinal
    for col in categorical_cols:
        if col in df_eda.columns:
            df_eda[col] = df_eda[col].astype(str).str.strip()
    
    # 1. Statistiques descriptives
    print("1. STATISTIQUES DESCRIPTIVES:")
    print(df_eda.describe(include='all').T)
    
    # 2. Distribution des variables numériques
    print("\n2. DISTRIBUTION DES VARIABLES NUMÉRIQUES:")
    plt.figure(figsize=(15, 10))
    valid_num_cols = [col for col in num_features if col in df_eda.columns and df_eda[col].dtype in [np.int64, np.float64]]
    
    if valid_num_cols:
        for i, col in enumerate(valid_num_cols[:6]):  # Maximum 6 plots
            plt.subplot(2, 3, i+1)
            sns.histplot(df_eda[col].dropna(), kde=True)
            plt.title(f'Distribution de {col}')
        plt.tight_layout()
        plt.savefig(f'distributions_num_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune variable numérique valide à afficher")
    
    # 3. Distribution des variables catégorielles
    print("\n3. DISTRIBUTION DES VARIABLES CATÉGORIELLES:")
    cat_features = cat_nominal + cat_ordinal
    valid_cat_cols = [col for col in cat_features if col in df_eda.columns and (df_eda[col].dtype == object or df_eda[col].dtype.name == 'category')]
    
    if valid_cat_cols:
        plt.figure(figsize=(18, 12))
        for i, col in enumerate(valid_cat_cols[:9]):  # Maximum 9 plots
            plt.subplot(3, 3, i+1)
            # Prendre les 10 catégories les plus fréquentes
            value_counts = df_eda[col].value_counts()
            top_categories = value_counts.nlargest(10).index
            filtered_data = df_eda[df_eda[col].isin(top_categories)]
            sns.countplot(data=filtered_data, x=col, order=top_categories)
            plt.title(f'Distribution de {col}')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'distributions_cat_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune variable catégorielle valide à afficher")
    
    # 4. Distribution de la cible
    print("\n4. DISTRIBUTION DE LA CIBLE:")
    plt.figure(figsize=(8, 5))
    sns.countplot(x='Target', data=df_eda)
    plt.title(f'Distribution de la variable cible ({dataset_name})')
    plt.savefig(f'distrib_target_{dataset_name}.png')
    plt.show()
    
    # 5. Valeurs manquantes
    print("\n5. VALEURS MANQUANTES:")
    missing = df_eda.isnull().sum()
    missing = missing[missing > 0]
    if len(missing) > 0:
        print("Variables avec valeurs manquantes:")
        print(missing)
        plt.figure(figsize=(10, 5))
        sns.barplot(x=missing.index, y=missing.values)
        plt.title('Valeurs manquantes par variable')
        plt.xticks(rotation=45)
        plt.savefig(f'missing_values_{dataset_name}.png')
        plt.show()
    else:
        print("Aucune valeur manquante détectée")
    
    # 6. Matrice de corrélation (uniquement pour les variables numériques)
    print("\n6. MATRICE DE CORRÉLATION (VARIABLES NUMÉRIQUES):")
    numeric_df = df_eda.select_dtypes(include=[np.number])
    if not numeric_df.empty and len(numeric_df.columns) > 1:
        plt.figure(figsize=(12, 10))
        corr = numeric_df.corr(method='spearman')
        sns.heatmap(corr, annot=True, fmt=".2f", cmap='coolwarm', 
                    mask=np.triu(np.ones_like(corr, dtype=bool)))
        plt.title(f'Matrice de corrélation ({dataset_name})')
        plt.savefig(f'correlation_matrix_{dataset_name}.png')
        plt.show()
    else:
        print("Pas assez de variables numériques pour la matrice de corrélation")
    
    return df_eda

#%% 4. MODÉLISATION ET ÉVALUATION
#%% 4. MODÉLISATION ET ÉVALUATION
def evaluate_models(X, y, preprocessor, dataset_name):
    """Entraîne, évalue et compare les modèles"""
    print(f"\n{'='*50}")
    print(f"MODÉLISATION - DATASET {dataset_name}")
    print(f"{'='*50}\n")
    
    # Vérifier la distribution des classes
    class_counts = y.value_counts()
    print(f"Distribution des classes: {class_counts.to_dict()}")
    
    # Split initial 70/30
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y)
    
    # Définition des modèles - version optimisée pour Revenu
    if dataset_name == "Revenu":
        models = {
            "Logistic Regression": LogisticRegression(max_iter=1000, class_weight='balanced', random_state=42),
            "Decision Tree": DecisionTreeClassifier(max_depth=5, random_state=42),
            "Random Forest": RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced'),
            "KNN": KNeighborsClassifier(n_neighbors=5),
            "Naive Bayes": GaussianNB(),
            "Gradient Boosting": GradientBoostingClassifier(n_estimators=50, random_state=42),
            "Linear SVM": LinearSVC(class_weight='balanced', dual=False, max_iter=1000, random_state=42)
        }
    else:
        models = {
            "Logistic Regression": LogisticRegression(max_iter=1000, class_weight='balanced', random_state=42),
            "Decision Tree": DecisionTreeClassifier(max_depth=5, random_state=42),
            "Random Forest": RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
            "KNN": KNeighborsClassifier(n_neighbors=5),
            "SVM": SVC(kernel='rbf', probability=True, random_state=42, class_weight='balanced'),
            "Naive Bayes": GaussianNB(),
            "Gradient Boosting": GradientBoostingClassifier(n_estimators=100, random_state=42)
        }
    
    # Stratégies d'équilibrage
    balance_strategies = {
        "Aucun": None,
        "SMOTE": SMOTE(random_state=42),
        "Sur-échantillonnage": RandomOverSampler(random_state=42),
        "Sous-échantillonnage": RandomUnderSampler(random_state=42)
    }
    
    # Métriques à collecter
    results = []
    
    # Évaluation de chaque modèle
    for model_name, model in models.items():
        for balance_name, balancer in balance_strategies.items():
            start_time = time.time()
            print(f"\n➤ Début: {datetime.now().strftime('%H:%M:%S')} - Modèle: {model_name} | Équilibrage: {balance_name}")
            
            try:
                # Création du pipeline
                if balancer:
                    pipeline = make_pipeline(
                        preprocessor,
                        balancer,
                        model
                    )
                else:
                    pipeline = make_pipeline(
                        preprocessor,
                        model
                    )
                
                # Entraînement
                pipeline.fit(X_train, y_train)
                
                # Prédictions
                y_pred = pipeline.predict(X_test)
                
                # Vérifier si le modèle peut prédire des probabilités
                if hasattr(pipeline[-1], "predict_proba"):
                    y_proba = pipeline.predict_proba(X_test)[:, 1]
                else:
                    # Pour LinearSVC, nous devons utiliser decision_function
                    if isinstance(model, LinearSVC):
                        decision = pipeline.decision_function(X_test)
                        y_proba = 1 / (1 + np.exp(-decision))
                    else:
                        y_proba = np.zeros(len(y_test))  # Valeur par défaut
                
                # Calcul des métriques
                metrics = {
                    "Dataset": dataset_name,
                    "Modèle": model_name,
                    "Équilibrage": balance_name,
                    "Accuracy": accuracy_score(y_test, y_pred),
                    "Precision": precision_score(y_test, y_pred, average='weighted'),
                    "Recall": recall_score(y_test, y_pred, average='weighted'),
                    "F1": f1_score(y_test, y_pred, average='weighted'),
                    "AUC": roc_auc_score(y_test, y_proba),
                    "Train_Score": pipeline.score(X_train, y_train),
                    "Test_Score": pipeline.score(X_test, y_test)
                }
                
                # Validation croisée (5 folds seulement pour accélérer)
                try:
                    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
                    scores = cross_validate(
                        pipeline, X_train, y_train, cv=cv,
                        scoring=['accuracy', 'f1_weighted'],
                        return_train_score=True
                    )
                    
                    # Stockage des résultats CV
                    cv_metrics = {
                        "CV_Train_Accuracy": np.mean(scores['train_accuracy']),
                        "CV_Test_Accuracy": np.mean(scores['test_accuracy']),
                        "CV_Train_F1": np.mean(scores['train_f1_weighted']),
                        "CV_Test_F1": np.mean(scores['test_f1_weighted'])
                    }
                    metrics.update(cv_metrics)
                except Exception as cv_error:
                    print(f"❌ Erreur CV: {str(cv_error)}")
                    metrics.update({
                        "CV_Train_Accuracy": np.nan,
                        "CV_Test_Accuracy": np.nan,
                        "CV_Train_F1": np.nan,
                        "CV_Test_F1": np.nan
                    })
                
                results.append(metrics)
                
                # Matrice de confusion
                cm = confusion_matrix(y_test, y_pred)
                plt.figure(figsize=(6, 4))
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                            xticklabels=['Négatif', 'Positif'], 
                            yticklabels=['Négatif', 'Positif'])
                plt.title(f"Matrice de confusion\n{model_name} | {balance_name}")
                plt.ylabel('Vérité terrain')
                plt.xlabel('Prédiction')
                plt.savefig(f"cm_{dataset_name}_{model_name}_{balance_name.replace(' ', '')}.png")
                plt.close()
                
                # Courbe ROC (seulement si le modèle peut prédire des probabilités)
                if hasattr(pipeline[-1], "predict_proba") or isinstance(model, LinearSVC):
                    plt.figure(figsize=(8, 6))
                    RocCurveDisplay.from_estimator(pipeline, X_test, y_test)
                    plt.title(f"Courbe ROC - {model_name} | {balance_name}")
                    plt.savefig(f"roc_{dataset_name}_{model_name}_{balance_name.replace(' ', '')}.png")
                    plt.close()
                    
            except Exception as e:
                print(f"❌ Erreur avec {model_name} | {balance_name}: {str(e)}")
            finally:
                duration = time.time() - start_time
                print(f"✓ Fin: {datetime.now().strftime('%H:%M:%S')} - Durée: {duration:.2f}s")
    
    # Conversion des résultats en DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # Sauvegarde des résultats
        results_df.to_csv(f"resultats_modeles_{dataset_name}.csv", index=False)
        
        # Affichage des meilleurs modèles
        print("\nMEILLEURS MODÈLES PAR MÉTRIQUE:")
        for metric in ['Accuracy', 'F1', 'AUC']:
            if metric in results_df.columns:
                best = results_df.nlargest(3, metric)
                print(f"\n▶ {metric}:")
                print(best[['Modèle', 'Équilibrage', metric, 'Train_Score', 'Test_Score']])
        
        # Analyse d'overfitting
        results_df['Overfitting_Gap'] = results_df['Train_Score'] - results_df['Test_Score']
        print("\nANALYSE D'OVERFITTING:")
        print(results_df[['Modèle', 'Équilibrage', 'Train_Score', 'Test_Score', 'Overfitting_Gap']])
        
        return results_df
    else:
        print("❌ Aucun résultat à afficher - tous les modèles ont échoué")
        return None

#%% 5. FONCTION PRINCIPALE
def main(datasets=None):
    """Exécute le pipeline complet pour les datasets spécifiés"""
    # Configuration des styles
    sns.set_style("whitegrid")
    plt.rcParams['font.size'] = 12
    plt.rcParams['figure.figsize'] = (10, 6)
    plt.rcParams['savefig.dpi'] = 300
    
    # Déterminer les datasets à traiter
    if datasets is None:
        datasets_to_process = ["Revenu", "Crédit"]
    else:
        datasets_to_process = datasets
    
    # Créer un dossier pour les résultats s'il n'existe pas
    if not os.path.exists('results'):
        os.makedirs('results')
    
    # Traitement pour chaque dataset
    for dataset in datasets_to_process:
        print(f"\n{'='*50}")
        print(f"TRAITEMENT DU DATASET: {dataset}")
        print(f"{'='*50}")
        
        try:
            # Chargement et prétraitement
            X, y, preprocessor, num, nom, ord = load_and_preprocess(dataset)
            
            # Analyse exploratoire
            df_eda = perform_eda(X, y, num, nom, ord, dataset)
            
            # Modélisation et évaluation
            results = evaluate_models(X, y, preprocessor, dataset)
            
            # Déplacer les résultats dans le dossier
            for file in os.listdir('.'):
                if file.endswith('.png') or file.endswith('.csv'):
                    if file.startswith(('distrib_', 'cm_', 'roc_', 'resultats_', 'boxplot_', 'correlation_', 'missing_')):
                        os.rename(file, os.path.join('results', file))
            
            # Synthèse des résultats
            if results is not None:
                print(f"\nSYNTHÈSE POUR {dataset}:")
                print(f"- Meilleur modèle F1: {results.nlargest(1, 'F1')[['Modèle', 'Équilibrage']].values[0]}")
                
                if 'AUC' in results.columns:
                    best_auc = results.nlargest(1, 'AUC')[['Modèle', 'Équilibrage']].values[0]
                    print(f"- Meilleur modèle AUC: {best_auc}")
                
                worst_overfitting = results.nlargest(1, 'Overfitting_Gap')[['Modèle', 'Équilibrage', 'Overfitting_Gap']].values[0]
                print(f"- Plus grand écart d'overfitting: {worst_overfitting}")
            
        except Exception as e:
            print(f"\n❌ ERREUR lors du traitement du dataset {dataset}: {str(e)}")
            continue

#%% EXÉCUTION
if __name__ == "__main__":
    # Gestion des arguments en ligne de commande
    datasets = None
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg == 'revenu':
            datasets = ["Revenu"]
        elif arg == 'credit':
            datasets = ["Crédit"]
        else:
            print("Usage: python script.py [revenu|credit]")
            sys.exit(1)
    
    main(datasets)