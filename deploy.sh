#!/bin/bash
# Script de déploiement automatique - TP1 INF5082

set -e  # Arrêter en cas d'erreur

echo "🚀 DÉPLOIEMENT DU MODÈLE TP1 INF5082"
echo "===================================="

# Variables de configuration
PROJECT_NAME="tp1-inf5082-model"
REMOTE_USER="ubuntu"  # Changez selon votre serveur
REMOTE_HOST="your-server-ip"  # Remplacez par l'IP de votre serveur
REMOTE_PATH="/opt/$PROJECT_NAME"

# Fonction d'aide
show_help() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  local     Déploiement local avec Docker"
    echo "  remote    Déploiement sur serveur distant"
    echo "  aws       Déploiement sur AWS EC2"
    echo "  gcp       Déploiement sur Google Cloud"
    echo "  azure     Déploiement sur Azure"
    echo "  help      Afficher cette aide"
    echo ""
}

# Déploiement local
deploy_local() {
    echo "📦 Déploiement local avec Docker..."
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker n'est pas installé"
        exit 1
    fi
    
    # Préparer le modèle
    echo "🔧 Préparation du modèle..."
    python save_best_model.py credit
    
    # Construire et lancer
    echo "🏗️ Construction de l'image Docker..."
    docker-compose build
    
    echo "🚀 Lancement des services..."
    docker-compose up -d
    
    echo "✅ Déploiement local terminé!"
    echo "🌐 API: http://localhost:5000"
    echo "📊 Monitoring: http://localhost:8501"
}

# Déploiement sur serveur distant
deploy_remote() {
    echo "🌐 Déploiement sur serveur distant..."
    
    # Vérifier la connexion SSH
    if ! ssh -o ConnectTimeout=5 $REMOTE_USER@$REMOTE_HOST "echo 'Connexion OK'"; then
        echo "❌ Impossible de se connecter au serveur"
        exit 1
    fi
    
    # Préparer le modèle localement
    echo "🔧 Préparation du modèle..."
    python save_best_model.py credit
    
    # Créer le répertoire sur le serveur
    ssh $REMOTE_USER@$REMOTE_HOST "sudo mkdir -p $REMOTE_PATH && sudo chown $REMOTE_USER:$REMOTE_USER $REMOTE_PATH"
    
    # Copier les fichiers
    echo "📤 Copie des fichiers..."
    rsync -avz --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' \
          ./ $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/
    
    # Installation sur le serveur
    ssh $REMOTE_USER@$REMOTE_HOST << EOF
        cd $REMOTE_PATH
        
        # Installer Docker si nécessaire
        if ! command -v docker &> /dev/null; then
            echo "📦 Installation de Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
        fi
        
        # Installer Docker Compose si nécessaire
        if ! command -v docker-compose &> /dev/null; then
            echo "📦 Installation de Docker Compose..."
            sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        fi
        
        # Lancer les services
        echo "🚀 Lancement des services..."
        docker-compose down || true
        docker-compose build
        docker-compose up -d
        
        echo "✅ Déploiement terminé!"
EOF
    
    echo "🌐 Votre modèle est accessible à:"
    echo "   API: http://$REMOTE_HOST:5000"
    echo "   Monitoring: http://$REMOTE_HOST:8501"
}

# Déploiement AWS EC2
deploy_aws() {
    echo "☁️ Déploiement sur AWS EC2..."
    
    # Vérifier AWS CLI
    if ! command -v aws &> /dev/null; then
        echo "❌ AWS CLI n'est pas installé"
        echo "Installez avec: pip install awscli"
        exit 1
    fi
    
    # Script de démarrage pour EC2
    cat > user-data.sh << 'EOF'
#!/bin/bash
yum update -y
yum install -y docker git

# Démarrer Docker
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Installer Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Cloner le projet (vous devrez adapter cette partie)
cd /opt
git clone YOUR_REPO_URL tp1-inf5082-model
cd tp1-inf5082-model

# Lancer les services
docker-compose up -d
EOF
    
    echo "📋 Script user-data.sh créé"
    echo "🔧 Créez une instance EC2 avec ce script de démarrage"
    echo "📖 Consultez le guide AWS dans la documentation"
}

# Déploiement Google Cloud
deploy_gcp() {
    echo "☁️ Déploiement sur Google Cloud..."
    
    # Vérifier gcloud CLI
    if ! command -v gcloud &> /dev/null; then
        echo "❌ Google Cloud CLI n'est pas installé"
        echo "Installez depuis: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Créer app.yaml pour App Engine
    cat > app.yaml << EOF
runtime: python311

env_variables:
  FLASK_ENV: production

automatic_scaling:
  min_instances: 1
  max_instances: 10

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10
EOF
    
    echo "📋 Configuration App Engine créée (app.yaml)"
    echo "🚀 Déployez avec: gcloud app deploy"
}

# Déploiement Azure
deploy_azure() {
    echo "☁️ Déploiement sur Azure..."
    
    # Vérifier Azure CLI
    if ! command -v az &> /dev/null; then
        echo "❌ Azure CLI n'est pas installé"
        echo "Installez depuis: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        exit 1
    fi
    
    echo "📖 Consultez le guide Azure dans la documentation"
}

# Menu principal
case "${1:-help}" in
    local)
        deploy_local
        ;;
    remote)
        deploy_remote
        ;;
    aws)
        deploy_aws
        ;;
    gcp)
        deploy_gcp
        ;;
    azure)
        deploy_azure
        ;;
    help|*)
        show_help
        ;;
esac
