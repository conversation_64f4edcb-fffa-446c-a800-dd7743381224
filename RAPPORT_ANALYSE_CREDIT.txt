RAPPORT D'ANALYSE CRISP-DM - CREDIT
============================================================
Date: 2025-06-21 14:28:37

=== ANALYSE DU DATASET CREDIT ===
Dimensions: (690, 16)
Variable cible: Approved
Distribution cible:
Approved
0    383
1    307
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
           Gender         Age        Debt  ...      ZipCode         Income    Approved
count  690.000000  690.000000  690.000000  ...   690.000000     690.000000  690.000000
mean     0.695652   31.514116    4.758725  ...   180.547826    1017.385507    0.444928
std      0.460464   11.860245    4.978163  ...   173.970323    5210.102598    0.497318
min      0.000000   13.750000    0.000000  ...     0.000000       0.000000    0.000000
25%      0.000000   22.670000    1.000000  ...    60.000000       0.000000    0.000000
50%      1.000000   28.460000    2.750000  ...   160.000000       5.000000    0.000000
75%      1.000000   37.707500    7.207500  ...   272.000000     395.500000    1.000000
max      1.000000   80.250000   28.000000  ...  2000.000000  100000.000000    1.000000

[8 rows x 13 columns]

=== INDICATEURS DE POSITION ET DISPERSION ===
Gender:
  IQR: 1.000, Asymétrie: -0.852, Aplatissement: -1.277
Age:
  IQR: 15.037, Asymétrie: 1.167, Aplatissement: 1.204
Debt:
  IQR: 6.207, Asymétrie: 1.489, Aplatissement: 2.274
Married:
  IQR: 0.000, Asymétrie: -1.226, Aplatissement: -0.499
BankCustomer:
  IQR: 0.000, Asymétrie: -1.245, Aplatissement: -0.452
YearsEmployed:
  IQR: 2.460, Asymétrie: 2.891, Aplatissement: 11.200
PriorDefault:
  IQR: 1.000, Asymétrie: -0.093, Aplatissement: -1.997
Employed:
  IQR: 1.000, Asymétrie: 0.294, Aplatissement: -1.919
CreditScore:
  IQR: 3.000, Asymétrie: 5.153, Aplatissement: 50.829
DriversLicense:
  IQR: 1.000, Asymétrie: 0.169, Aplatissement: -1.977
ZipCode:
  IQR: 212.000, Asymétrie: 2.702, Aplatissement: 19.224
Income:
  IQR: 395.500, Asymétrie: 13.141, Aplatissement: 214.670

=== VALEURS MANQUANTES ===
Series([], dtype: int64)

Variables numériques: ['Gender', 'Age', 'Debt', 'Married', 'BankCustomer', 'YearsEmployed', 'PriorDefault', 'Employed', 'CreditScore', 'DriversLicense', 'ZipCode', 'Income']
Variables catégorielles: ['Industry', 'Ethnicity', 'Citizen']

=== DÉTECTION D'OUTLIERS (méthode IQR) ===
Gender: 0 outliers (0.0%)
Age: 18 outliers (2.6%)
Debt: 17 outliers (2.5%)
Married: 165 outliers (23.9%)
BankCustomer: 163 outliers (23.6%)
YearsEmployed: 63 outliers (9.1%)
PriorDefault: 0 outliers (0.0%)
Employed: 0 outliers (0.0%)
CreditScore: 79 outliers (11.4%)
DriversLicense: 0 outliers (0.0%)
ZipCode: 12 outliers (1.7%)
Income: 113 outliers (16.4%)

=== CORRÉLATIONS AVEC LA CIBLE ===
PriorDefault: 0.720
Employed: 0.458
CreditScore: 0.406
YearsEmployed: 0.322
Debt: 0.206

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 12 variables
Imputation catégorielle (mode) pour 3 variables
Label Encoding pour Industry
One-Hot Encoding pour Ethnicity: 4 nouvelles variables
One-Hot Encoding pour Citizen: 2 nouvelles variables
StandardScaler appliqué à 19 variables
Split 70/30: Train=(483, 19), Test=(207, 19)
SMOTE appliqué: 536 échantillons équilibrés

=== SUGGESTIONS DE FEATURE ENGINEERING ===
1. Transformation log pour 'Age' (asymétrie: 1.17)
2. Transformation log pour 'Debt' (asymétrie: 1.49)
3. Transformation log pour 'Married' (asymétrie: -1.23)
4. Transformation log pour 'BankCustomer' (asymétrie: -1.24)
5. Transformation log pour 'YearsEmployed' (asymétrie: 2.89)
6. Transformation log pour 'CreditScore' (asymétrie: 5.15)
7. Transformation log pour 'ZipCode' (asymétrie: 2.70)
8. Transformation log pour 'Income' (asymétrie: 13.14)
9. Traitement outliers pour 'Married' (23.9%)
10. Traitement outliers pour 'BankCustomer' (23.6%)
11. Traitement outliers pour 'YearsEmployed' (9.1%)
12. Traitement outliers pour 'CreditScore' (11.4%)
13. Traitement outliers pour 'Income' (16.4%)

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8844 | Acc: 0.8841 | Over: -0.0128
Decision Tree        | F1: 0.8122 | Acc: 0.8116 | Over: 0.1884
Random Forest        | F1: 0.8744 | Acc: 0.8744 | Over: 0.1256
KNN                  | F1: 0.8600 | Acc: 0.8599 | Over: 0.0151
SVM                  | F1: 0.8844 | Acc: 0.8841 | Over: 0.0171
Naive Bayes          | F1: 0.8126 | Acc: 0.8164 | Over: 0.0007
Gradient Boosting    | F1: 0.9036 | Acc: 0.9034 | Over: 0.0556

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Gradient Boosting    | F1-CV: 0.8470 (±0.0288)
Logistic Regression  | F1-CV: 0.8469 (±0.0488)
SVM                  | F1-CV: 0.8674 (±0.0444)

--- Validation croisée 7-folds ---
Gradient Boosting    | F1-CV: 0.8471 (±0.0583)
Logistic Regression  | F1-CV: 0.8544 (±0.0530)
SVM                  | F1-CV: 0.8714 (±0.0519)

--- Validation croisée 10-folds ---
Gradient Boosting    | F1-CV: 0.8582 (±0.0510)
Logistic Regression  | F1-CV: 0.8469 (±0.0500)
SVM                  | F1-CV: 0.8638 (±0.0471)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Gradient Boosting    0.9036   0.9034   0.9485   0.0556      
Logistic Regression  0.8844   0.8841   0.9566   -0.0128     
SVM                  0.8844   0.8841   0.9467   0.0171      
Random Forest        0.8744   0.8744   0.9553   0.1256      
KNN                  0.8600   0.8599   0.9177   0.0151      
Naive Bayes          0.8126   0.8164   0.9255   0.0007      
Decision Tree        0.8122   0.8116   0.8141   0.1884      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Gradient Boosting
Performance F1: 0.9036

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Gradient Boosting

              precision    recall  f1-score   support

           0       0.93      0.90      0.91       115
           1       0.88      0.91      0.89        92

    accuracy                           0.90       207
   macro avg       0.90      0.90      0.90       207
weighted avg       0.90      0.90      0.90       207


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.0557
✅ Pas d'overfitting significatif détecté

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Gradient Boosting
   - Performance F1: 0.9036
   - Accuracy: 0.9034

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
