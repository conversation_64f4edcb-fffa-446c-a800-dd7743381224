RAPPORT D'ANALYSE CRISP-DM - CREDIT
============================================================
Date: 2025-06-21 13:38:42

=== ANALYSE DU DATASET CREDIT ===
Dimensions: (690, 16)
Variable cible: Approved
Distribution cible:
Approved
0    383
1    307
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
           Gender         Age        Debt     Married  ...  DriversLicense      ZipCode         Income    Approved
count  690.000000  690.000000  690.000000  690.000000  ...      690.000000   690.000000     690.000000  690.000000
mean     0.695652   31.514116    4.758725    0.760870  ...        0.457971   180.547826    1017.385507    0.444928
std      0.460464   11.860245    4.978163    0.426862  ...        0.498592   173.970323    5210.102598    0.497318
min      0.000000   13.750000    0.000000    0.000000  ...        0.000000     0.000000       0.000000    0.000000
25%      0.000000   22.670000    1.000000    1.000000  ...        0.000000    60.000000       0.000000    0.000000
50%      1.000000   28.460000    2.750000    1.000000  ...        0.000000   160.000000       5.000000    0.000000
75%      1.000000   37.707500    7.207500    1.000000  ...        1.000000   272.000000     395.500000    1.000000
max      1.000000   80.250000   28.000000    1.000000  ...        1.000000  2000.000000  100000.000000    1.000000

[8 rows x 13 columns]

=== VALEURS MANQUANTES ===
Series([], dtype: int64)

Variables numériques: ['Gender', 'Age', 'Debt', 'Married', 'BankCustomer', 'YearsEmployed', 'PriorDefault', 'Employed', 'CreditScore', 'DriversLicense', 'ZipCode', 'Income']
Variables catégorielles: ['Industry', 'Ethnicity', 'Citizen']

=== CORRÉLATIONS AVEC LA CIBLE ===
PriorDefault: 0.720
Employed: 0.458
CreditScore: 0.406
YearsEmployed: 0.322
Debt: 0.206

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 12 variables
Imputation catégorielle (mode) pour 3 variables
Label Encoding pour Industry
One-Hot Encoding pour Ethnicity: 4 nouvelles variables
One-Hot Encoding pour Citizen: 2 nouvelles variables
StandardScaler appliqué à 19 variables
Split 70/30: Train=(483, 19), Test=(207, 19)
SMOTE appliqué: 536 échantillons équilibrés

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8844 | Acc: 0.8841 | Over: -0.0128
Decision Tree        | F1: 0.8122 | Acc: 0.8116 | Over: 0.1884
Random Forest        | F1: 0.8744 | Acc: 0.8744 | Over: 0.1256
KNN                  | F1: 0.8600 | Acc: 0.8599 | Over: 0.0151
SVM                  | F1: 0.8844 | Acc: 0.8841 | Over: 0.0171
Naive Bayes          | F1: 0.8126 | Acc: 0.8164 | Over: 0.0007
Gradient Boosting    | F1: 0.9036 | Acc: 0.9034 | Over: 0.0556

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Gradient Boosting    | F1-CV: 0.8470 (±0.0288)
Logistic Regression  | F1-CV: 0.8469 (±0.0488)
SVM                  | F1-CV: 0.8674 (±0.0444)

--- Validation croisée 7-folds ---
Gradient Boosting    | F1-CV: 0.8471 (±0.0583)
Logistic Regression  | F1-CV: 0.8544 (±0.0530)
SVM                  | F1-CV: 0.8714 (±0.0519)

--- Validation croisée 10-folds ---
Gradient Boosting    | F1-CV: 0.8582 (±0.0510)
Logistic Regression  | F1-CV: 0.8469 (±0.0500)
SVM                  | F1-CV: 0.8638 (±0.0471)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Gradient Boosting    0.9036   0.9034   0.9485   0.0556      
Logistic Regression  0.8844   0.8841   0.9566   -0.0128     
SVM                  0.8844   0.8841   0.9467   0.0171      
Random Forest        0.8744   0.8744   0.9553   0.1256      
KNN                  0.8600   0.8599   0.9177   0.0151      
Naive Bayes          0.8126   0.8164   0.9255   0.0007      
Decision Tree        0.8122   0.8116   0.8141   0.1884      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Gradient Boosting
Performance F1: 0.9036

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Gradient Boosting

              precision    recall  f1-score   support

           0       0.93      0.90      0.91       115
           1       0.88      0.91      0.89        92

    accuracy                           0.90       207
   macro avg       0.90      0.90      0.90       207
weighted avg       0.90      0.90      0.90       207


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.0557
✅ Pas d'overfitting significatif détecté

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Gradient Boosting
   - Performance F1: 0.9036
   - Accuracy: 0.9034

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
