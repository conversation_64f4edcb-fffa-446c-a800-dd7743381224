RAPPORT D'ANALYSE CRISP-DM - CREDIT
============================================================
Date: 2025-06-21 16:19:42

=== ANALYSE DU DATASET CREDIT ===
Dimensions: (690, 16)
Variable cible: Approved
Distribution cible:
Approved
0    383
1    307
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
           Gender         Age        Debt  ...      ZipCode         Income    Approved
count  690.000000  690.000000  690.000000  ...   690.000000     690.000000  690.000000
mean     0.695652   31.514116    4.758725  ...   180.547826    1017.385507    0.444928
std      0.460464   11.860245    4.978163  ...   173.970323    5210.102598    0.497318
min      0.000000   13.750000    0.000000  ...     0.000000       0.000000    0.000000
25%      0.000000   22.670000    1.000000  ...    60.000000       0.000000    0.000000
50%      1.000000   28.460000    2.750000  ...   160.000000       5.000000    0.000000
75%      1.000000   37.707500    7.207500  ...   272.000000     395.500000    1.000000
max      1.000000   80.250000   28.000000  ...  2000.000000  100000.000000    1.000000

[8 rows x 13 columns]

=== INDICATEURS DE POSITION ET DISPERSION ===
Gender: IQR: 1.000, Asymétrie: -0.852, Aplatissement: -1.277
Age: IQR: 15.037, Asymétrie: 1.167, Aplatissement: 1.204
Debt: IQR: 6.207, Asymétrie: 1.489, Aplatissement: 2.274
Married: IQR: 0.000, Asymétrie: -1.226, Aplatissement: -0.499
BankCustomer: IQR: 0.000, Asymétrie: -1.245, Aplatissement: -0.452
YearsEmployed: IQR: 2.460, Asymétrie: 2.891, Aplatissement: 11.200
PriorDefault: IQR: 1.000, Asymétrie: -0.093, Aplatissement: -1.997
Employed: IQR: 1.000, Asymétrie: 0.294, Aplatissement: -1.919
CreditScore: IQR: 3.000, Asymétrie: 5.153, Aplatissement: 50.829
DriversLicense: IQR: 1.000, Asymétrie: 0.169, Aplatissement: -1.977
ZipCode: IQR: 212.000, Asymétrie: 2.702, Aplatissement: 19.224
Income: IQR: 395.500, Asymétrie: 13.141, Aplatissement: 214.670

=== VALEURS MANQUANTES ===
Series([], dtype: int64)

Variables numériques: ['Gender', 'Age', 'Debt', 'Married', 'BankCustomer', 'YearsEmployed', 'PriorDefault', 'Employed', 'CreditScore', 'DriversLicense', 'ZipCode', 'Income']
Variables catégorielles: ['Industry', 'Ethnicity', 'Citizen']

=== DÉTECTION D'OUTLIERS (méthode IQR) ===
Gender: 0 outliers (0.0%)
Age: 18 outliers (2.6%)
Debt: 17 outliers (2.5%)
Married: 165 outliers (23.9%)
BankCustomer: 163 outliers (23.6%)
YearsEmployed: 63 outliers (9.1%)
PriorDefault: 0 outliers (0.0%)
Employed: 0 outliers (0.0%)
CreditScore: 79 outliers (11.4%)
DriversLicense: 0 outliers (0.0%)
ZipCode: 12 outliers (1.7%)
Income: 113 outliers (16.4%)

=== COMPARAISON CORRÉLATIONS PEARSON vs SPEARMAN ===
Age vs YearsEmployed: Pearson=0.391, Spearman=0.254 (diff=0.137)
PriorDefault vs CreditScore: Pearson=0.380, Spearman=0.483 (diff=0.104)
PriorDefault vs Income: Pearson=0.090, Spearman=0.194 (diff=0.104)
Employed vs CreditScore: Pearson=0.571, Spearman=0.952 (diff=0.380)
Employed vs Income: Pearson=0.078, Spearman=0.408 (diff=0.330)
CreditScore vs Income: Pearson=0.064, Spearman=0.427 (diff=0.364)
ZipCode vs Income: Pearson=0.059, Spearman=-0.055 (diff=0.114)

=== CORRÉLATIONS AVEC LA CIBLE ===
PriorDefault: 0.720
Employed: 0.458
CreditScore: 0.406
YearsEmployed: 0.322
Debt: 0.206

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 12 variables
Justification: Médiane choisie car plus robuste aux outliers détectés
Justification: 'Unknown' si >20% manquant, sinon mode
Label Encoding pour Industry
One-Hot Encoding pour Ethnicity: 4 nouvelles variables
One-Hot Encoding pour Citizen: 2 nouvelles variables
MinMaxScaler appliqué à 19 variables
Justification: MinMaxScaler choisi car présence d'outliers significatifs
Split 70/30: Train=(483, 19), Test=(207, 19)
SMOTE appliqué: 536 échantillons équilibrés

=== SUGGESTIONS DE FEATURE ENGINEERING ===
1. Transformation log pour 'Age' (asymétrie: 1.17)
2. Transformation log pour 'Debt' (asymétrie: 1.49)
3. Transformation log pour 'Married' (asymétrie: -1.23)
4. Transformation log pour 'BankCustomer' (asymétrie: -1.24)
5. Transformation log pour 'YearsEmployed' (asymétrie: 2.89)
6. Transformation log pour 'CreditScore' (asymétrie: 5.15)
7. Transformation log pour 'ZipCode' (asymétrie: 2.70)
8. Transformation log pour 'Income' (asymétrie: 13.14)
9. Traitement outliers pour 'Married' (23.9%)
10. Traitement outliers pour 'BankCustomer' (23.6%)
11. Traitement outliers pour 'YearsEmployed' (9.1%)
12. Traitement outliers pour 'CreditScore' (11.4%)
13. Traitement outliers pour 'Income' (16.4%)

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8892 | Acc: 0.8889 | Over: -0.0176
Decision Tree        | F1: 0.8506 | Acc: 0.8502 | Over: 0.1498
Random Forest        | F1: 0.8890 | Acc: 0.8889 | Over: 0.1111
KNN                  | F1: 0.8553 | Acc: 0.8551 | Over: 0.0162
SVM                  | F1: 0.8892 | Acc: 0.8889 | Over: -0.0214
Naive Bayes          | F1: 0.8230 | Acc: 0.8261 | Over: -0.0183
Gradient Boosting    | F1: 0.8988 | Acc: 0.8986 | Over: 0.0679

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Gradient Boosting    | F1-CV: 0.8414 (±0.0314)
Logistic Regression  | F1-CV: 0.8543 (±0.0416)
SVM                  | F1-CV: 0.8487 (±0.0432)

--- Validation croisée 7-folds ---
Gradient Boosting    | F1-CV: 0.8414 (±0.0512)
Logistic Regression  | F1-CV: 0.8544 (±0.0586)
SVM                  | F1-CV: 0.8432 (±0.0563)

--- Validation croisée 10-folds ---
Gradient Boosting    | F1-CV: 0.8506 (±0.0505)
Logistic Regression  | F1-CV: 0.8542 (±0.0426)
SVM                  | F1-CV: 0.8449 (±0.0479)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Gradient Boosting    0.8988   0.8986   0.9519   0.0679      
Logistic Regression  0.8892   0.8889   0.9464   -0.0176     
SVM                  0.8892   0.8889   0.9521   -0.0214     
Random Forest        0.8890   0.8889   0.9576   0.1111      
KNN                  0.8553   0.8551   0.9301   0.0162      
Decision Tree        0.8506   0.8502   0.8511   0.1498      
Naive Bayes          0.8230   0.8261   0.9243   -0.0183     

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Gradient Boosting
Performance F1: 0.8988

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Gradient Boosting

              precision    recall  f1-score   support

           0       0.94      0.88      0.91       115
           1       0.86      0.92      0.89        92

    accuracy                           0.90       207
   macro avg       0.90      0.90      0.90       207
weighted avg       0.90      0.90      0.90       207


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.0411
✅ Pas d'overfitting significatif détecté

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Gradient Boosting
   - Performance F1: 0.8988
   - Accuracy: 0.8986

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
