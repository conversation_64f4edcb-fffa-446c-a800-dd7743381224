#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pour sauvegarder le meilleur modèle pour déploiement
TP1 INF5082 - Sélection et préparation du modèle de production
"""

import pandas as pd
import numpy as np
import pickle
import os
from datetime import datetime
import sys

# Importer les modules du TP
from tp1_crispdm import load_and_preprocess, feature_engineering
from deployment_module import ModelDeployment

# Importer les modèles sklearn
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import StandardScaler, OneHotEncoder, OrdinalEncoder
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline

def select_and_save_best_model(dataset_name="Crédit"):
    """
    Sélectionne et sauvegarde le meilleur modèle pour déploiement
    
    Args:
        dataset_name: "Crédit" ou "Revenu"
    """
    print(f"🔍 Sélection du meilleur modèle pour le dataset {dataset_name}")
    print("=" * 60)
    
    # 1. Chargement et préparation des données
    print("1. Chargement des données...")
    X, y, preprocessor, num_features, cat_nominal, cat_ordinal = load_and_preprocess(dataset_name)
    
    # 2. Feature engineering
    print("2. Feature engineering...")
    X_enhanced, new_features = feature_engineering(X, dataset_name)
    
    # Mise à jour du préprocesseur si nécessaire
    if new_features:
        new_cat_features = []
        for feat in new_features:
            if feat in X_enhanced.columns and X_enhanced[feat].dtype == 'object':
                new_cat_features.append(feat)
        
        updated_nom = cat_nominal + new_cat_features
        updated_num = [col for col in X_enhanced.columns if col in num_features and col in X_enhanced.columns]
        
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', Pipeline(steps=[
                    ('imputer', SimpleImputer(strategy='median')),
                    ('scaler', StandardScaler())]), updated_num),
                ('nom', Pipeline(steps=[
                    ('imputer', SimpleImputer(strategy='most_frequent')),
                    ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))]), updated_nom),
                ('ord', Pipeline(steps=[
                    ('imputer', SimpleImputer(strategy='most_frequent')),
                    ('encoder', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))
                ]), cat_ordinal)
            ], remainder='drop')
    
    # 3. Split des données
    print("3. Division des données...")
    X_train, X_test, y_train, y_test = train_test_split(
        X_enhanced, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 4. Définition des modèles candidats
    print("4. Évaluation des modèles candidats...")
    
    models = {
        "Random Forest": RandomForestClassifier(
            n_estimators=100, 
            random_state=42, 
            class_weight='balanced'
        ),
        "Gradient Boosting": GradientBoostingClassifier(
            n_estimators=100, 
            random_state=42
        ),
        "Logistic Regression": LogisticRegression(
            max_iter=1000, 
            class_weight='balanced', 
            random_state=42
        ),
        "SVM": SVC(
            kernel='rbf', 
            probability=True, 
            random_state=42, 
            class_weight='balanced'
        )
    }
    
    # 5. Évaluation et sélection du meilleur modèle
    best_model = None
    best_score = 0
    best_name = ""
    results = {}
    
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    for name, model in models.items():
        print(f"   Évaluation: {name}")
        
        # Créer le pipeline complet
        pipeline = Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', model)
        ])
        
        # Validation croisée
        cv_scores = cross_val_score(pipeline, X_train, y_train, cv=cv, scoring='f1_weighted')
        mean_score = cv_scores.mean()
        std_score = cv_scores.std()
        
        # Entraînement sur tout le train set
        pipeline.fit(X_train, y_train)
        
        # Score sur le test set
        test_score = pipeline.score(X_test, y_test)
        
        results[name] = {
            'cv_mean': mean_score,
            'cv_std': std_score,
            'test_score': test_score,
            'pipeline': pipeline
        }
        
        print(f"     CV F1: {mean_score:.3f} (±{std_score:.3f})")
        print(f"     Test Score: {test_score:.3f}")
        
        # Sélection du meilleur modèle basé sur la validation croisée
        if mean_score > best_score:
            best_score = mean_score
            best_model = pipeline
            best_name = name
    
    print(f"\n🏆 Meilleur modèle sélectionné: {best_name}")
    print(f"   Score CV F1: {best_score:.3f}")
    print(f"   Score Test: {results[best_name]['test_score']:.3f}")
    
    # 6. Évaluation détaillée du meilleur modèle
    print(f"\n6. Évaluation détaillée du modèle {best_name}:")
    
    y_pred = best_model.predict(X_test)
    
    print("\nRapport de classification:")
    print(classification_report(y_test, y_pred))
    
    print("\nMatrice de confusion:")
    print(confusion_matrix(y_test, y_pred))
    
    # 7. Sauvegarde du modèle pour déploiement
    print("\n7. Sauvegarde du modèle pour déploiement...")
    
    # Créer le dossier models s'il n'existe pas
    os.makedirs("models", exist_ok=True)
    
    # Configuration du modèle
    model_config = {
        'name': best_name,
        'dataset': dataset_name,
        'version': f"1.0_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        'cv_score': best_score,
        'test_score': results[best_name]['test_score'],
        'features': list(X_enhanced.columns),
        'target_classes': list(np.unique(y)),
        'training_date': datetime.now().isoformat(),
        'n_training_samples': len(X_train),
        'n_test_samples': len(X_test)
    }
    
    # Initialiser le module de déploiement
    deployment = ModelDeployment("models/best_model.pkl")
    
    # Sauvegarder le modèle
    success = deployment.save_model(
        model=best_model,
        preprocessor=None,  # Le preprocessor est déjà dans le pipeline
        config=model_config
    )
    
    if success:
        print(f"✅ Modèle sauvegardé avec succès: models/best_model.pkl")
        print(f"   Version: {model_config['version']}")
        
        # Sauvegarder aussi la configuration séparément
        with open("models/model_config.json", 'w') as f:
            import json
            json.dump(model_config, f, indent=2)
        
        print("✅ Configuration sauvegardée: models/model_config.json")
        
        # Créer un fichier README pour le déploiement
        readme_content = f"""# Modèle de Production - {dataset_name}

## Informations du Modèle
- **Nom**: {best_name}
- **Dataset**: {dataset_name}
- **Version**: {model_config['version']}
- **Date d'entraînement**: {model_config['training_date']}

## Performances
- **Score CV F1**: {best_score:.3f}
- **Score Test**: {results[best_name]['test_score']:.3f}

## Données d'entraînement
- **Échantillons d'entraînement**: {len(X_train)}
- **Échantillons de test**: {len(X_test)}
- **Variables**: {len(X_enhanced.columns)}

## Utilisation
1. Démarrer l'API: `python model_api.py`
2. Accéder à l'interface: http://localhost:5000
3. Utiliser l'endpoint /predict pour les prédictions

## Monitoring
- Logs: dossier `logs/`
- Métriques: dossier `monitoring/`
- Alertes automatiques configurées
"""
        
        with open("models/README.md", 'w') as f:
            f.write(readme_content)
        
        print("✅ Documentation créée: models/README.md")
        
        return True
    else:
        print("❌ Erreur lors de la sauvegarde du modèle")
        return False

def main():
    """Fonction principale"""
    dataset = "Crédit"  # Par défaut
    
    if len(sys.argv) > 1:
        if sys.argv[1].lower() == "revenu":
            dataset = "Revenu"
        elif sys.argv[1].lower() == "credit":
            dataset = "Crédit"
        else:
            print("Usage: python save_best_model.py [revenu|credit]")
            sys.exit(1)
    
    print(f"🚀 Préparation du modèle de production pour {dataset}")
    print("=" * 60)
    
    success = select_and_save_best_model(dataset)
    
    if success:
        print("\n🎉 Modèle prêt pour le déploiement!")
        print("\nÉtapes suivantes:")
        print("1. Installer Flask: pip install flask")
        print("2. Démarrer l'API: python model_api.py")
        print("3. Accéder à l'interface: http://localhost:5000")
        print("4. Tester les prédictions via l'API")
    else:
        print("\n❌ Échec de la préparation du modèle")
        sys.exit(1)

if __name__ == "__main__":
    main()
