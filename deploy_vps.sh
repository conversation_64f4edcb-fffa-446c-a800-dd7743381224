#!/bin/bash
# Script de déploiement automatique pour tp1_inf5082

set -e

SERVER_IP=""
SERVER_USER="ubuntu"
PROJECT_PATH="/opt/tp1_inf5082"

echo "🚀 Déploiement sur VPS $SERVER_IP"

# Préparer le modèle
python save_best_model.py credit

# Copier les fichiers
rsync -avz --exclude='.git' --exclude='__pycache__' \
      ./ $SERVER_USER@$SERVER_IP:$PROJECT_PATH/

# Installation sur le serveur
ssh $SERVER_USER@$SERVER_IP << 'EOF'
cd tp1_inf5082

# Installer Docker si nécessaire
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# Installer Docker Compose
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Lancer les services
docker-compose down || true
docker-compose build
docker-compose up -d

echo "✅ Déploiement terminé!"
EOF

echo "🌐 Votre modèle est accessible à:"
echo "   API: http://$SERVER_IP:5000"
echo "   Monitoring: http://$SERVER_IP:8501"
