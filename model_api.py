#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API REST pour le déploiement des modèles - TP1 INF5082
Serveur Flask pour servir les modèles en production
"""

from flask import Flask, request, jsonify, render_template_string
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import os
from deployment_module import ModelDeployment

# Configuration de l'application Flask
app = Flask(__name__)
app.config['JSON_SORT_KEYS'] = False

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialisation du module de déploiement
deployment = ModelDeployment("models/best_model.pkl")

# Template HTML pour l'interface web
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>API Modèle de Classification - TP1 INF5082</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint { background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007bff; }
        .status { padding: 5px 10px; border-radius: 3px; color: white; }
        .status.healthy { background-color: #28a745; }
        .status.warning { background-color: #ffc107; color: black; }
        .status.error { background-color: #dc3545; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API Modèle de Classification</h1>
        <p style="text-align: center; color: #666;">TP1 INF5082 - Déploiement et Monitoring</p>
        
        <div class="section">
            <h2>📊 Statut du Service</h2>
            <p>Statut: <span class="status healthy">🟢 Opérationnel</span></p>
            <p>Dernière mise à jour: {{ timestamp }}</p>
            <p>Version du modèle: {{ version }}</p>
        </div>
        
        <div class="section">
            <h2>🔗 Endpoints Disponibles</h2>
            
            <div class="endpoint">
                <p><span class="method">GET</span> <code>/</code></p>
                <p>Interface web principale</p>
            </div>
            
            <div class="endpoint">
                <p><span class="method">GET</span> <code>/health</code></p>
                <p>Vérification de l'état du service</p>
            </div>
            
            <div class="endpoint">
                <p><span class="method">POST</span> <code>/predict</code></p>
                <p>Prédiction sur nouvelles données</p>
                <p><strong>Format JSON:</strong></p>
                <pre>{"data": [{"feature1": value1, "feature2": value2, ...}]}</pre>
            </div>
            
            <div class="endpoint">
                <p><span class="method">GET</span> <code>/monitoring</code></p>
                <p>Rapport de monitoring en temps réel</p>
            </div>
            
            <div class="endpoint">
                <p><span class="method">POST</span> <code>/feedback</code></p>
                <p>Envoi de feedback pour monitoring</p>
                <p><strong>Format JSON:</strong></p>
                <pre>{"predictions": [0, 1, 1], "true_labels": [0, 1, 0]}</pre>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 Test de Prédiction</h2>
            <form id="predictionForm">
                <div class="form-group">
                    <label>Données JSON (exemple pour dataset Crédit):</label>
                    <textarea id="jsonData" rows="8" placeholder='{"data": [{"Gender": 1, "Age": 30, "Debt": 2.5, "Married": 1, "BankCustomer": 1, "Industry": "Energy", "Ethnicity": "White", "YearsEmployed": 2.0, "PriorDefault": 0, "Employed": 1, "CreditScore": 5, "DriversLicense": 1, "Citizen": "ByBirth", "ZipCode": "00100", "Income": 1000}]}'></textarea>
                </div>
                <button type="button" onclick="testPrediction()">Tester la Prédiction</button>
            </form>
            <div id="result" style="margin-top: 20px;"></div>
        </div>
    </div>
    
    <script>
        function testPrediction() {
            const data = document.getElementById('jsonData').value;
            const resultDiv = document.getElementById('result');
            
            try {
                JSON.parse(data); // Validation JSON
                
                fetch('/predict', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: data
                })
                .then(response => response.json())
                .then(result => {
                    resultDiv.innerHTML = '<h3>Résultat:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultDiv.innerHTML = '<h3 style="color: red;">Erreur:</h3><pre>' + error + '</pre>';
                });
            } catch (e) {
                resultDiv.innerHTML = '<h3 style="color: red;">Erreur JSON:</h3><pre>' + e.message + '</pre>';
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    """Page d'accueil avec interface web"""
    return render_template_string(HTML_TEMPLATE, 
                                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                version=deployment.config.get('version', '1.0'))

@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint de vérification de santé"""
    try:
        # Vérifier que le modèle est chargé
        if deployment.model is None:
            deployment.load_model()
        
        status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'model_loaded': deployment.model is not None,
            'version': deployment.config.get('version', '1.0'),
            'uptime': 'OK'
        }
        
        return jsonify(status), 200
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/predict', methods=['POST'])
def predict():
    """Endpoint de prédiction"""
    try:
        # Vérifier que le modèle est chargé
        if deployment.model is None:
            if not deployment.load_model():
                return jsonify({'error': 'Modèle non disponible'}), 500
        
        # Récupérer les données
        data = request.get_json()
        
        if not data or 'data' not in data:
            return jsonify({'error': 'Format de données invalide. Utilisez {"data": [...]}'}), 400
        
        # Convertir en DataFrame
        df = pd.DataFrame(data['data'])
        
        # Effectuer la prédiction
        result = deployment.predict(df)
        
        logger.info(f"Prédiction effectuée pour {len(df)} échantillons")
        
        return jsonify({
            'success': True,
            'result': result,
            'n_samples': len(df)
        }), 200
        
    except Exception as e:
        logger.error(f"Erreur lors de la prédiction: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/feedback', methods=['POST'])
def feedback():
    """Endpoint pour recevoir du feedback et calculer les métriques"""
    try:
        data = request.get_json()
        
        if not data or 'predictions' not in data or 'true_labels' not in data:
            return jsonify({'error': 'Format invalide. Utilisez {"predictions": [...], "true_labels": [...]}'}), 400
        
        predictions = data['predictions']
        true_labels = data['true_labels']
        
        if len(predictions) != len(true_labels):
            return jsonify({'error': 'Longueurs différentes entre prédictions et vraies étiquettes'}), 400
        
        # Calculer les métriques de performance
        metrics = deployment.monitor_performance(true_labels, predictions)
        
        logger.info(f"Feedback reçu pour {len(predictions)} échantillons")
        
        return jsonify({
            'success': True,
            'metrics': metrics,
            'n_samples': len(predictions)
        }), 200
        
    except Exception as e:
        logger.error(f"Erreur lors du traitement du feedback: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/monitoring', methods=['GET'])
def monitoring():
    """Endpoint pour le rapport de monitoring"""
    try:
        report = deployment.generate_monitoring_report()
        
        return jsonify({
            'success': True,
            'report': report,
            'alerts': deployment.alerts[-10:],  # 10 dernières alertes
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Erreur lors de la génération du rapport: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint non trouvé'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Erreur interne du serveur'}), 500

if __name__ == '__main__':
    # Créer les dossiers nécessaires
    os.makedirs("models", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("monitoring", exist_ok=True)
    
    # Charger le modèle au démarrage
    if os.path.exists("models/best_model.pkl"):
        deployment.load_model()
        logger.info("Modèle chargé au démarrage")
    else:
        logger.warning("Aucun modèle trouvé. Utilisez save_best_model.py pour créer un modèle.")
    
    # Lancer le serveur
    logger.info("Démarrage du serveur API...")
    app.run(host='0.0.0.0', port=5000, debug=False)
