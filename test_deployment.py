#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de Test du Déploiement - TP1 INF5082
Validation complète du pipeline de déploiement
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

def test_model_preparation():
    """Test 1: Préparation du modèle"""
    print("🧪 Test 1: Préparation du modèle")
    print("-" * 40)
    
    # Vérifier l'existence des fichiers
    required_files = [
        "models/best_model.pkl",
        "models/model_config.json",
        "models/README.md"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            print(f"❌ {file} - MANQUANT")
            all_exist = False
    
    if all_exist:
        print("✅ Test 1 RÉUSSI: Tous les fichiers du modèle sont présents")
        return True
    else:
        print("❌ Test 1 ÉCHOUÉ: Fichiers manquants")
        return False

def test_deployment_module():
    """Test 2: Module de déploiement"""
    print("\n🧪 Test 2: Module de déploiement")
    print("-" * 40)
    
    try:
        from deployment_module import ModelDeployment
        
        # Initialiser le module
        deployment = ModelDeployment("models/best_model.pkl")
        
        # Tester le chargement du modèle
        if deployment.load_model():
            print("✅ Chargement du modèle - OK")
        else:
            print("❌ Chargement du modèle - ÉCHEC")
            return False
        
        # Tester la génération de rapport
        report = deployment.generate_monitoring_report()
        if report and len(report) > 100:
            print("✅ Génération de rapport - OK")
        else:
            print("❌ Génération de rapport - ÉCHEC")
            return False
        
        print("✅ Test 2 RÉUSSI: Module de déploiement fonctionnel")
        return True
        
    except Exception as e:
        print(f"❌ Test 2 ÉCHOUÉ: {e}")
        return False

def test_api_endpoints(base_url="http://localhost:5000"):
    """Test 3: Endpoints de l'API"""
    print("\n🧪 Test 3: Endpoints de l'API")
    print("-" * 40)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ GET /health - OK")
            tests_passed += 1
        else:
            print(f"❌ GET /health - Code {response.status_code}")
    except Exception as e:
        print(f"❌ GET /health - Erreur: {e}")
    
    # Test 2: Page d'accueil
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ GET / - OK")
            tests_passed += 1
        else:
            print(f"❌ GET / - Code {response.status_code}")
    except Exception as e:
        print(f"❌ GET / - Erreur: {e}")
    
    # Test 3: Prédiction
    try:
        test_data = {
            "data": [{
                "Gender": 1,
                "Age": 30,
                "Debt": 2.5,
                "Married": 1,
                "BankCustomer": 1,
                "Industry": "Energy",
                "Ethnicity": "White",
                "YearsEmployed": 2.0,
                "PriorDefault": 0,
                "Employed": 1,
                "CreditScore": 5,
                "DriversLicense": 1,
                "Citizen": "ByBirth",
                "ZipCode": "00100",
                "Income": 1000
            }]
        }
        
        response = requests.post(
            f"{base_url}/predict",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result and 'predictions' in result['result']:
                print("✅ POST /predict - OK")
                print(f"   Prédiction: {result['result']['predictions'][0]}")
                tests_passed += 1
            else:
                print("❌ POST /predict - Format de réponse invalide")
        else:
            print(f"❌ POST /predict - Code {response.status_code}")
    except Exception as e:
        print(f"❌ POST /predict - Erreur: {e}")
    
    # Test 4: Monitoring
    try:
        response = requests.get(f"{base_url}/monitoring", timeout=5)
        if response.status_code == 200:
            print("✅ GET /monitoring - OK")
            tests_passed += 1
        else:
            print(f"❌ GET /monitoring - Code {response.status_code}")
    except Exception as e:
        print(f"❌ GET /monitoring - Erreur: {e}")
    
    if tests_passed == total_tests:
        print(f"✅ Test 3 RÉUSSI: {tests_passed}/{total_tests} endpoints fonctionnels")
        return True
    else:
        print(f"❌ Test 3 PARTIEL: {tests_passed}/{total_tests} endpoints fonctionnels")
        return False

def test_performance_monitoring(base_url="http://localhost:5000"):
    """Test 4: Monitoring des performances"""
    print("\n🧪 Test 4: Monitoring des performances")
    print("-" * 40)
    
    try:
        # Simuler du feedback
        feedback_data = {
            "predictions": [1, 0, 1, 1, 0],
            "true_labels": [1, 0, 0, 1, 0]
        }
        
        response = requests.post(
            f"{base_url}/feedback",
            json=feedback_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'metrics' in result:
                metrics = result['metrics']
                print("✅ Feedback envoyé - OK")
                print(f"   Accuracy: {metrics.get('accuracy', 'N/A'):.3f}")
                print(f"   F1-Score: {metrics.get('f1', 'N/A'):.3f}")
                
                print("✅ Test 4 RÉUSSI: Monitoring des performances fonctionnel")
                return True
            else:
                print("❌ Réponse de feedback invalide")
                return False
        else:
            print(f"❌ POST /feedback - Code {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test 4 ÉCHOUÉ: {e}")
        return False

def test_load_performance(base_url="http://localhost:5000", n_requests=10):
    """Test 5: Performance sous charge"""
    print(f"\n🧪 Test 5: Performance sous charge ({n_requests} requêtes)")
    print("-" * 40)
    
    try:
        test_data = {
            "data": [{
                "Gender": 1, "Age": 30, "Debt": 2.5, "Married": 1,
                "BankCustomer": 1, "Industry": "Energy", "Ethnicity": "White",
                "YearsEmployed": 2.0, "PriorDefault": 0, "Employed": 1,
                "CreditScore": 5, "DriversLicense": 1, "Citizen": "ByBirth",
                "ZipCode": "00100", "Income": 1000
            }]
        }
        
        response_times = []
        successful_requests = 0
        
        print("Envoi des requêtes...", end="")
        
        for i in range(n_requests):
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{base_url}/predict",
                    json=test_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    response_times.append(response_time)
                    successful_requests += 1
                    print(".", end="")
                else:
                    print("x", end="")
                    
            except Exception:
                print("x", end="")
        
        print()  # Nouvelle ligne
        
        if successful_requests > 0:
            avg_response_time = np.mean(response_times)
            max_response_time = np.max(response_times)
            min_response_time = np.min(response_times)
            
            print(f"✅ Requêtes réussies: {successful_requests}/{n_requests}")
            print(f"   Temps de réponse moyen: {avg_response_time:.3f}s")
            print(f"   Temps de réponse min: {min_response_time:.3f}s")
            print(f"   Temps de réponse max: {max_response_time:.3f}s")
            
            if avg_response_time < 2.0 and successful_requests >= n_requests * 0.9:
                print("✅ Test 5 RÉUSSI: Performance acceptable sous charge")
                return True
            else:
                print("⚠️ Test 5 PARTIEL: Performance dégradée")
                return False
        else:
            print("❌ Test 5 ÉCHOUÉ: Aucune requête réussie")
            return False
            
    except Exception as e:
        print(f"❌ Test 5 ÉCHOUÉ: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TESTS DE DÉPLOIEMENT - TP1 INF5082")
    print("=" * 50)
    print(f"Début des tests: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Vérifier si l'API est accessible
    api_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        api_running = response.status_code == 200
    except:
        api_running = False
    
    if not api_running:
        print(f"\n⚠️ L'API n'est pas accessible à {api_url}")
        print("Veuillez démarrer l'API avec: python model_api.py")
        print("\nExécution des tests hors-ligne uniquement...\n")
    
    # Exécuter les tests
    tests_results = []
    
    # Test 1: Préparation du modèle
    tests_results.append(test_model_preparation())
    
    # Test 2: Module de déploiement
    tests_results.append(test_deployment_module())
    
    if api_running:
        # Test 3: Endpoints API
        tests_results.append(test_api_endpoints(api_url))
        
        # Test 4: Monitoring
        tests_results.append(test_performance_monitoring(api_url))
        
        # Test 5: Performance
        tests_results.append(test_load_performance(api_url, 5))
    else:
        print("\n⏭️ Tests API ignorés (API non accessible)")
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed_tests = sum(tests_results)
    total_tests = len(tests_results)
    
    print(f"Tests réussis: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Votre déploiement est prêt pour la production!")
    elif passed_tests >= total_tests * 0.8:
        print("⚠️ DÉPLOIEMENT PARTIELLEMENT FONCTIONNEL")
        print("Quelques améliorations sont nécessaires.")
    else:
        print("❌ DÉPLOIEMENT NON FONCTIONNEL")
        print("Veuillez corriger les erreurs avant la mise en production.")
    
    print(f"\nFin des tests: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
