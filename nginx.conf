events {
    worker_connections 1024;
}

http {
    upstream model_api {
        server model-api:5000;
    }
    
    upstream monitoring_dashboard {
        server monitoring-dashboard:8501;
    }

    # Configuration pour l'API
    server {
        listen 80;
        server_name your-domain.com;  # Remplacez par votre domaine
        
        # Redirection HTTPS (optionnel)
        # return 301 https://$server_name$request_uri;
        
        # API du modèle
        location /api/ {
            proxy_pass http://model_api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 120s;
            proxy_connect_timeout 120s;
        }
        
        # Dashboard de monitoring
        location /monitoring/ {
            proxy_pass http://monitoring_dashboard/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Support pour Streamlit
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        # Page d'accueil par défaut vers l'API
        location / {
            proxy_pass http://model_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Configuration HTTPS (optionnel)
    # server {
    #     listen 443 ssl;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     # Même configuration que HTTP mais avec SSL
    # }
}
