#!/usr/bin/env python3
"""
INF5082 - TP1 : Analyse Exploratoire et Modélisation de Données
Méthodologie CRISP-DM - Version Refactorisée

Auteur: [Votre nom]
Date: 2025-06-21
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder, MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,
                           roc_auc_score, confusion_matrix, classification_report, roc_curve)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from imblearn.over_sampling import SMOTE
import warnings
import sys
import os
from io import StringIO

warnings.filterwarnings('ignore')

class VisualizationHelper:
    """Classe utilitaire pour les visualisations"""

    @staticmethod
    def calculate_grid_size(n_items, max_cols=4):
        """Calcule la taille de grille optimale"""
        if n_items <= 6:
            return 3, 2, (15, 8)
        elif n_items <= 12:
            return 4, 3, (20, 12)
        else:
            return max_cols, (n_items + max_cols - 1) // max_cols, (25, 16)

    @staticmethod
    def save_plot(filename, dpi=300):
        """Sauvegarde standardisée des graphiques"""
        plt.tight_layout()
        plt.savefig(filename, dpi=dpi, bbox_inches='tight')
        plt.close()

class StatisticsHelper:
    """Classe utilitaire pour les calculs statistiques"""

    @staticmethod
    def calculate_outliers_iqr(data):
        """Calcule les outliers avec la méthode IQR"""
        if len(data) == 0:
            return [], 0

        Q1, Q3 = data.quantile([0.25, 0.75])
        IQR = Q3 - Q1
        limite_inf = Q1 - 1.5 * IQR
        limite_sup = Q3 + 1.5 * IQR
        outliers = data[(data < limite_inf) | (data > limite_sup)]
        pourcentage = (len(outliers) / len(data)) * 100
        return outliers, pourcentage

    @staticmethod
    def calculate_advanced_stats(data):
        """Calcule les statistiques avancées"""
        if len(data) == 0:
            return None

        q1, q3 = data.quantile([0.25, 0.75])
        return {
            'iqr': q3 - q1,
            'asymetrie': data.skew(),
            'aplatissement': data.kurtosis()
        }

class CRISPDMAnalyzer:
    """Classe principale pour l'analyse CRISP-DM"""

    def __init__(self, dataset_type):
        self.dataset_type = dataset_type
        self.rapport = StringIO()
        self.viz_helper = VisualizationHelper()
        self.stats_helper = StatisticsHelper()

        # Configuration des modèles
        self.modeles = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Decision Tree': DecisionTreeClassifier(random_state=42),
            'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
            'KNN': KNeighborsClassifier(n_neighbors=5),
            'SVM': SVC(random_state=42, probability=True),
            'Naive Bayes': GaussianNB(),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42, n_estimators=100)
        }

        # Variables ordinales prédéfinies
        self.variables_ordinales = {
            'education': ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', '10th', '11th', '12th',
                         'HS-grad', 'Some-college', 'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        }

    def log(self, message):
        """Enregistre dans le rapport"""
        self.rapport.write(message + '\n')

    def console(self, message):
        """Affiche sur la console"""
        print(message)

    def _identifier_types_variables(self):
        """Identifie les types de variables"""
        self.vars_numeriques = self.donnees.select_dtypes(include=[np.number]).columns.tolist()
        if self.colonne_cible in self.vars_numeriques:
            self.vars_numeriques.remove(self.colonne_cible)

        self.vars_categorielles = self.donnees.select_dtypes(include=['object']).columns.tolist()
        if self.colonne_cible in self.vars_categorielles:
            self.vars_categorielles.remove(self.colonne_cible)

    def _analyser_outliers(self):
        """Analyse les outliers pour toutes les variables numériques"""
        self.log(f"\n=== DÉTECTION D'OUTLIERS (méthode IQR) ===")
        self.outliers_info = {}

        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0:
                outliers, pourcentage = self.stats_helper.calculate_outliers_iqr(data)
                self.outliers_info[var] = {'nombre': len(outliers), 'pourcentage': pourcentage}
                self.log(f"{var}: {len(outliers)} outliers ({pourcentage:.1f}%)")

    def _analyser_correlations(self):
        """Analyse les corrélations Pearson et Spearman"""
        if len(self.vars_numeriques) <= 1:
            return

        # Calcul des corrélations
        corr_pearson = self.donnees[self.vars_numeriques].corr(method='pearson')
        corr_spearman = self.donnees[self.vars_numeriques].corr(method='spearman')

        # Visualisation
        _, axes = plt.subplots(1, 2, figsize=(20, 8))

        sns.heatmap(corr_pearson, annot=True, cmap='coolwarm', center=0,
                   linewidths=0.5, ax=axes[0], fmt='.2f')
        axes[0].set_title(f'Corrélation Pearson - {self.dataset_type.upper()}')

        sns.heatmap(corr_spearman, annot=True, cmap='coolwarm', center=0,
                   linewidths=0.5, ax=axes[1], fmt='.2f')
        axes[1].set_title(f'Corrélation Spearman - {self.dataset_type.upper()}')

        self.viz_helper.save_plot(f'{self.dataset_type}_correlation_heatmaps.png')

        # Analyse des différences
        self._comparer_correlations(corr_pearson, corr_spearman)
        self._analyser_correlation_cible()

    def _comparer_correlations(self, pearson, spearman):
        """Compare les corrélations Pearson vs Spearman"""
        self.log(f"\n=== COMPARAISON CORRÉLATIONS PEARSON vs SPEARMAN ===")

        for i, var1 in enumerate(self.vars_numeriques):
            for j, var2 in enumerate(self.vars_numeriques):
                if i < j:
                    pearson_val = pearson.loc[var1, var2]
                    spearman_val = spearman.loc[var1, var2]
                    diff = abs(pearson_val - spearman_val)
                    if diff > 0.1:
                        self.log(f"{var1} vs {var2}: Pearson={pearson_val:.3f}, Spearman={spearman_val:.3f} (diff={diff:.3f})")

    def _analyser_correlation_cible(self):
        """Analyse les corrélations avec la variable cible"""
        if self.colonne_cible not in self.donnees.columns:
            return

        le_temp = LabelEncoder()
        y_encoded = le_temp.fit_transform(self.donnees[self.colonne_cible])
        correlations = []

        for var in self.vars_numeriques:
            corr = np.corrcoef(self.donnees[var].fillna(0), y_encoded)[0,1]
            correlations.append((var, abs(corr)))

        correlations.sort(key=lambda x: x[1], reverse=True)
        self.log(f"\n=== CORRÉLATIONS AVEC LA CIBLE ===")
        for var, corr in correlations[:5]:
            self.log(f"{var}: {corr:.3f}")

    def _creer_visualisation_simple(self, data, plot_type, filename, title, **kwargs):
        """Crée une visualisation simple standardisée"""
        plt.figure(figsize=kwargs.get('figsize', (8, 6)))

        if plot_type == 'bar':
            data.plot(kind='bar')
            plt.xticks(rotation=kwargs.get('rotation', 0))
        elif plot_type == 'hist':
            data.hist(bins=kwargs.get('bins', 30), alpha=kwargs.get('alpha', 0.7))
        elif plot_type == 'boxplot':
            plt.boxplot(data.dropna())

        plt.title(title)
        if 'xlabel' in kwargs:
            plt.xlabel(kwargs['xlabel'])
        if 'ylabel' in kwargs:
            plt.ylabel(kwargs['ylabel'])

        self.viz_helper.save_plot(filename)

    def _creer_grille_visualisations(self, variables, plot_func, filename_prefix, plot_type):
        """Crée une grille de visualisations pour plusieurs variables"""
        if not variables:
            return

        n_vars = len(variables)
        n_cols, n_rows, figsize = self.viz_helper.calculate_grid_size(n_vars)

        plt.figure(figsize=figsize)

        for i, var in enumerate(variables):
            if i < n_cols * n_rows:
                plt.subplot(n_rows, n_cols, i+1)
                plot_func(var, i)

        self.viz_helper.save_plot(f'{self.dataset_type}_{filename_prefix}.png')

    def _plot_histogram(self, var, index):
        """Plot un histogramme pour une variable"""
        self.donnees[var].hist(bins=30, alpha=0.7)
        plt.title(f'Distribution de {var}')
        plt.xlabel(var)
        plt.ylabel('Fréquence')

    def _plot_boxplot(self, var, index):
        """Plot un boxplot pour une variable"""
        plt.boxplot(self.donnees[var].dropna())
        plt.title(f'Boxplot de {var}')
        plt.ylabel(var)

    def _plot_countplot(self, var, index):
        """Plot un countplot pour une variable catégorielle"""
        top_categories = self.donnees[var].value_counts().head(10)
        top_categories.plot(kind='bar')
        plt.title(f'Distribution de {var}')
        plt.xlabel(var)
        plt.ylabel('Fréquence')
        plt.xticks(rotation=45)
        
    def charger_donnees(self):
        """1. COMPRÉHENSION DES DONNÉES"""
        self.console("📊 Phase 1: Chargement et compréhension des données...")
        
        fichier = f'./{self.dataset_type}.csv'
        self.donnees = pd.read_csv(fichier)
        
        # Nettoyage des valeurs manquantes
        self.donnees = self.donnees.replace('?', np.nan)

        # Optimisation pour gros datasets
        if len(self.donnees) > 10000:
            self.console(f"⚡ Dataset volumineux détecté ({len(self.donnees)} échantillons)")
            self.console("⚡ Réduction à 10000 échantillons pour accélérer l'analyse...")
            self.donnees = self.donnees.sample(n=10000, random_state=42)
        
        # Détection automatique de la variable cible
        if self.dataset_type == 'credit':
            self.colonne_cible = 'Approved'
        else:  # revenu
            self.colonne_cible = self.donnees.columns[-1]  # Dernière colonne
            
        self.log(f"=== ANALYSE DU DATASET {self.dataset_type.upper()} ===")
        self.log(f"Dimensions: {self.donnees.shape}")
        self.log(f"Variable cible: {self.colonne_cible}")
        self.log(f"Distribution cible:\n{self.donnees[self.colonne_cible].value_counts()}")
        
        self.console(f"✅ Dataset {self.dataset_type} chargé: {self.donnees.shape}")
        
    def analyser_donnees(self):
        """2. ANALYSE EXPLORATOIRE (EDA) - 20 points"""
        self.console("📈 Phase 2: Analyse exploratoire des données...")

        # Identifier les types de variables
        self._identifier_types_variables()

        # Statistiques descriptives
        self.log("\n=== STATISTIQUES DESCRIPTIVES ===")
        self.log(str(self.donnees.describe()))

        # Statistiques avancées
        self._analyser_statistiques_avancees()

        # Valeurs manquantes
        self._analyser_valeurs_manquantes()

        # Détection d'outliers
        self._analyser_outliers()

        # Visualisations essentielles
        self._creer_visualisations_eda()

    def _analyser_statistiques_avancees(self):
        """Analyse les statistiques avancées pour variables numériques"""
        if not self.vars_numeriques:
            return

        self.log(f"\n=== INDICATEURS DE POSITION ET DISPERSION ===")
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            stats = self.stats_helper.calculate_advanced_stats(data)
            if stats:
                self.log(f"{var}:")
                self.log(f"  IQR: {stats['iqr']:.3f}, Asymétrie: {stats['asymetrie']:.3f}, Aplatissement: {stats['aplatissement']:.3f}")

    def _analyser_valeurs_manquantes(self):
        """Analyse les valeurs manquantes"""
        valeurs_manquantes = self.donnees.isnull().sum()
        self.log(f"\n=== VALEURS MANQUANTES ===")
        self.log(str(valeurs_manquantes[valeurs_manquantes > 0]))

        self.log(f"\nVariables numériques: {self.vars_numeriques}")
        self.log(f"Variables catégorielles: {self.vars_categorielles}")
        
    def _creer_visualisations_eda(self):
        """Crée les visualisations essentielles pour l'EDA"""

        # 1. Distribution de la variable cible
        self._creer_visualisation_simple(
            self.donnees[self.colonne_cible].value_counts(),
            'bar',
            f'{self.dataset_type}_target_distribution.png',
            f'Distribution de la variable cible - {self.dataset_type.upper()}',
            figsize=(8, 6),
            rotation=0
        )

        # 2. Matrices de corrélation
        self._analyser_correlations()

        # 3. Histogrammes des variables numériques
        self._creer_grille_visualisations(
            self.vars_numeriques,
            self._plot_histogram,
            'histograms',
            'histogram'
        )

        # 4. Boxplots pour détecter les outliers
        self._creer_grille_visualisations(
            self.vars_numeriques,
            self._plot_boxplot,
            'boxplots',
            'boxplot'
        )

        # 5. Countplots pour variables catégorielles
        self._creer_grille_visualisations(
            self.vars_categorielles,
            self._plot_countplot,
            'countplots',
            'countplot'
        )
            
    def preprocesser_donnees(self):
        """3. PRÉPARATION DES DONNÉES - 15 points"""
        self.console("⚙️ Phase 3: Prétraitement des données...")
        
        self.log(f"\n=== PRÉTRAITEMENT DES DONNÉES ===")
        
        # Séparation X et y
        X = self.donnees.drop(columns=[self.colonne_cible])
        y = self.donnees[self.colonne_cible]
        
        # Encodage de la variable cible
        self.label_encoder_y = LabelEncoder()
        y_encoded = self.label_encoder_y.fit_transform(y)
        
        # Imputation des valeurs manquantes
        # Variables numériques (médiane ET moyenne pour comparaison)
        if self.vars_numeriques:
            # Stratégie principale: médiane (plus robuste aux outliers)
            imputer_num = SimpleImputer(strategy='median')
            X[self.vars_numeriques] = imputer_num.fit_transform(X[self.vars_numeriques])
            self.log(f"Imputation numérique (médiane) pour {len(self.vars_numeriques)} variables")

            # Log de justification du choix
            self.log("Justification: Médiane choisie car plus robuste aux outliers détectés")

        # Variables catégorielles (mode ET modalité "Unknown")
        if self.vars_categorielles:
            for var in self.vars_categorielles:
                valeurs_manquantes = X[var].isnull().sum()
                if valeurs_manquantes > 0:
                    pourcentage_manquant = (valeurs_manquantes / len(X)) * 100

                    if pourcentage_manquant > 20:  # Beaucoup de valeurs manquantes
                        # Utiliser "Unknown" pour préserver l'information de manque
                        X[var] = X[var].fillna('Unknown')
                        self.log(f"Imputation '{var}': 'Unknown' ({pourcentage_manquant:.1f}% manquant)")
                    else:
                        # Utiliser le mode pour peu de valeurs manquantes
                        mode_value = X[var].mode()[0] if not X[var].mode().empty else 'Unknown'
                        X[var] = X[var].fillna(mode_value)
                        self.log(f"Imputation '{var}': mode='{mode_value}' ({pourcentage_manquant:.1f}% manquant)")

            self.log("Justification: 'Unknown' si >20% manquant, sinon mode")
        
        # Encodage des variables catégorielles
        X_encoded = X.copy()

        # Variables ordinales spécifiques (ordre défini)
        variables_ordinales = {
            'education': ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', '10th', '11th', '12th',
                         'HS-grad', 'Some-college', 'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
        }

        for var in self.vars_categorielles:
            if var in variables_ordinales:
                # OrdinalEncoder pour variables ordinales
                categories_ordonnees = variables_ordinales[var]
                # Filtrer les catégories présentes dans les données
                categories_presentes = [cat for cat in categories_ordonnees if cat in X[var].unique()]
                if len(categories_presentes) > 1:
                    ordinal_encoder = OrdinalEncoder(categories=[categories_presentes], handle_unknown='use_encoded_value', unknown_value=-1)
                    X_encoded[var] = ordinal_encoder.fit_transform(X[[var]]).flatten()
                    self.log(f"Ordinal Encoding pour {var}: {len(categories_presentes)} niveaux ordonnés")
                else:
                    # Fallback vers Label Encoding si pas assez de catégories
                    le = LabelEncoder()
                    X_encoded[var] = le.fit_transform(X[var].astype(str))
                    self.log(f"Label Encoding pour {var} (fallback)")
            elif X[var].nunique() <= 10:  # One-hot pour peu de modalités
                dummies = pd.get_dummies(X[var], prefix=var, drop_first=True)
                X_encoded = pd.concat([X_encoded.drop(columns=[var]), dummies], axis=1)
                self.log(f"One-Hot Encoding pour {var}: {dummies.shape[1]} nouvelles variables")
            else:  # Label encoding pour beaucoup de modalités
                le = LabelEncoder()
                X_encoded[var] = le.fit_transform(X[var].astype(str))
                self.log(f"Label Encoding pour {var}")
        
        # Normalisation / Standardisation
        # Choix intelligent selon la distribution des données
        has_outliers = hasattr(self, 'outliers_info') and any(
            info['pourcentage'] > 10 for info in self.outliers_info.values()
        )

        if has_outliers:
            # MinMaxScaler plus robuste avec beaucoup d'outliers
            scaler = MinMaxScaler()
            scaler_name = "MinMaxScaler"
            justification = "MinMaxScaler choisi car présence d'outliers significatifs"
        else:
            # StandardScaler par défaut
            scaler = StandardScaler()
            scaler_name = "StandardScaler"
            justification = "StandardScaler choisi car distribution relativement normale"

        X_scaled = scaler.fit_transform(X_encoded)
        self.log(f"{scaler_name} appliqué à {X_scaled.shape[1]} variables")
        self.log(f"Justification: {justification}")
        
        # Split 70/30
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X_scaled, y_encoded, test_size=0.3, random_state=42, stratify=y_encoded
        )
        
        self.log(f"Split 70/30: Train={self.X_train.shape}, Test={self.X_test.shape}")
        
        # Équilibrage avec SMOTE
        smote = SMOTE(random_state=42)
        self.X_train_balanced, self.y_train_balanced = smote.fit_resample(self.X_train, self.y_train)
        
        self.log(f"SMOTE appliqué: {self.X_train_balanced.shape[0]} échantillons équilibrés")

        # Suggestions de feature engineering
        self.log(f"\n=== SUGGESTIONS DE FEATURE ENGINEERING ===")
        suggestions = []

        # Basé sur l'asymétrie des variables
        for var in self.vars_numeriques:
            data = self.donnees[var].dropna()
            if len(data) > 0 and abs(data.skew()) > 1:
                suggestions.append(f"Transformation log pour '{var}' (asymétrie: {data.skew():.2f})")

        # Basé sur les outliers détectés
        if hasattr(self, 'outliers_info'):
            for var, info in self.outliers_info.items():
                if info['pourcentage'] > 5:
                    suggestions.append(f"Traitement outliers pour '{var}' ({info['pourcentage']:.1f}%)")

        # Variables catégorielles avec trop de modalités
        for var in self.vars_categorielles:
            if self.donnees[var].nunique() > 20:
                suggestions.append(f"Regroupement de catégories pour '{var}' ({self.donnees[var].nunique()} modalités)")

        # Affichage des suggestions
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                self.log(f"{i}. {suggestion}")
        else:
            self.log("Aucune suggestion spécifique identifiée")
        
    def modeliser(self):
        """4. MODÉLISATION - 25 points"""
        self.console("🤖 Phase 4: Modélisation avec 7 algorithmes...")
        
        self.resultats = {}
        
        self.log(f"\n=== ÉVALUATION DES 7 ALGORITHMES ===")
        
        for nom, modele in self.modeles.items():
            try:
                # Entraînement
                modele.fit(self.X_train_balanced, self.y_train_balanced)
                
                # Prédictions
                y_train_pred = modele.predict(self.X_train_balanced)
                y_test_pred = modele.predict(self.X_test)
                
                # Métriques
                train_acc = accuracy_score(self.y_train_balanced, y_train_pred)
                test_acc = accuracy_score(self.y_test, y_test_pred)
                precision = precision_score(self.y_test, y_test_pred, average='weighted')
                recall = recall_score(self.y_test, y_test_pred, average='weighted')
                f1 = f1_score(self.y_test, y_test_pred, average='weighted')
                
                # AUC-ROC
                try:
                    y_proba = modele.predict_proba(self.X_test)[:, 1]
                    auc = roc_auc_score(self.y_test, y_proba)
                except:
                    auc = 0
                
                # Overfitting
                overfitting = train_acc - test_acc
                
                self.resultats[nom] = {
                    'train_accuracy': train_acc,
                    'test_accuracy': test_acc,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc_roc': auc,
                    'overfitting': overfitting
                }
                
                self.log(f"{nom:20} | F1: {f1:.4f} | Acc: {test_acc:.4f} | Over: {overfitting:.4f}")
                
            except Exception as e:
                self.log(f"{nom:20} | ERREUR: {str(e)}")
                
    def valider(self):
        """5. VALIDATION ET COMPARAISON - 15 points"""
        self.console("📊 Phase 5: Validation croisée...")
        
        self.log(f"\n=== VALIDATION CROISÉE ===")
        
        # Validation croisée pour les 3 meilleurs modèles
        df_resultats = pd.DataFrame(self.resultats).T
        top3 = df_resultats.nlargest(3, 'f1_score')
        
        for fold in [5, 7, 10]:
            self.log(f"\n--- Validation croisée {fold}-folds ---")
            cv = StratifiedKFold(n_splits=fold, shuffle=True, random_state=42)
            
            for nom in top3.index:
                modele = self.modeles[nom]
                scores = cross_val_score(modele, self.X_train_balanced, self.y_train_balanced, 
                                       cv=cv, scoring='f1_weighted')
                self.log(f"{nom:20} | F1-CV: {scores.mean():.4f} (±{scores.std():.4f})")

    def evaluer(self):
        """6. ÉVALUATION ET ANALYSE - 10 points"""
        self.console("📈 Phase 6: Évaluation et analyse des résultats...")

        # Tableau comparatif
        df_resultats = pd.DataFrame(self.resultats).T
        df_resultats = df_resultats.sort_values('f1_score', ascending=False)

        self.log(f"\n=== TABLEAU COMPARATIF DES RÉSULTATS ===")
        self.log(f"{'Modèle':<20} {'F1':<8} {'Accuracy':<8} {'AUC':<8} {'Overfitting':<12}")
        self.log("-" * 60)

        for nom, row in df_resultats.iterrows():
            self.log(f"{nom:<20} {row['f1_score']:<8.4f} {row['test_accuracy']:<8.4f} "
                    f"{row['auc_roc']:<8.4f} {row['overfitting']:<12.4f}")

        # Meilleur modèle
        self.meilleur_modele = df_resultats.index[0]
        self.log(f"\n=== MEILLEUR MODÈLE ===")
        self.log(f"Modèle sélectionné: {self.meilleur_modele}")
        self.log(f"Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")

        # Matrice de confusion du meilleur modèle
        modele_final = self.modeles[self.meilleur_modele]
        modele_final.fit(self.X_train_balanced, self.y_train_balanced)
        y_pred_final = modele_final.predict(self.X_test)

        # Visualisation de la matrice de confusion
        plt.figure(figsize=(8, 6))
        cm = confusion_matrix(self.y_test, y_pred_final)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'Matrice de confusion - {self.meilleur_modele}')
        plt.ylabel('Réalité')
        plt.xlabel('Prédiction')
        plt.tight_layout()
        plt.savefig(f'{self.dataset_type}_confusion_matrix.png', dpi=300)
        plt.close()

        # Classification report
        self.log(f"\n=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===")
        self.log(f"Modèle: {self.meilleur_modele}")
        self.log(f"\n{classification_report(self.y_test, y_pred_final)}")

        # Analyse de l'overfitting
        self.log(f"\n=== ANALYSE DE L'OVERFITTING ===")
        overfitting_moyen = df_resultats['overfitting'].mean()
        self.log(f"Overfitting moyen: {overfitting_moyen:.4f}")

        if overfitting_moyen > 0.1:
            self.log("⚠️ Overfitting détecté - Recommandations:")
            self.log("- Réduire la complexité des modèles")
            self.log("- Augmenter la régularisation")
            self.log("- Collecter plus de données")
        else:
            self.log("✅ Pas d'overfitting significatif détecté")

        # Courbe ROC pour le meilleur modèle
        if df_resultats.loc[self.meilleur_modele, 'auc_roc'] > 0:
            from sklearn.metrics import roc_curve
            y_proba = modele_final.predict_proba(self.X_test)[:, 1]
            fpr, tpr, _ = roc_curve(self.y_test, y_proba)

            plt.figure(figsize=(8, 6))
            plt.plot(fpr, tpr, linewidth=2,
                    label=f'{self.meilleur_modele} (AUC = {df_resultats.loc[self.meilleur_modele, "auc_roc"]:.3f})')
            plt.plot([0, 1], [0, 1], 'k--', alpha=0.6, label='Classification aléatoire')
            plt.xlabel('Taux de Faux Positifs')
            plt.ylabel('Taux de Vrais Positifs')
            plt.title(f'Courbe ROC - {self.dataset_type.upper()}')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(f'{self.dataset_type}_roc_curve.png', dpi=300)
            plt.close()

    def generer_rapport(self):
        """7. GÉNÉRATION DU RAPPORT FINAL"""
        self.console("📄 Phase 7: Génération du rapport...")

        # Recommandations
        df_resultats = pd.DataFrame(self.resultats).T

        self.log(f"\n=== RECOMMANDATIONS ===")
        self.log(f"1. MODÈLE RECOMMANDÉ: {self.meilleur_modele}")
        self.log(f"   - Performance F1: {df_resultats.loc[self.meilleur_modele, 'f1_score']:.4f}")
        self.log(f"   - Accuracy: {df_resultats.loc[self.meilleur_modele, 'test_accuracy']:.4f}")

        self.log(f"\n2. POINTS CLÉS:")
        self.log(f"   - {len(self.modeles)} algorithmes évalués")
        self.log(f"   - Split 70/30 avec validation croisée")
        self.log(f"   - Équilibrage SMOTE appliqué")
        self.log(f"   - Prétraitement complet réalisé")

        self.log(f"\n3. AMÉLIORATIONS POSSIBLES:")
        self.log(f"   - Optimisation des hyperparamètres")
        self.log(f"   - Feature engineering avancé")
        self.log(f"   - Ensemble methods")

        # Sauvegarde du rapport
        nom_rapport = f'RAPPORT_ANALYSE_{self.dataset_type.upper()}.txt'
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write(f"RAPPORT D'ANALYSE CRISP-DM - {self.dataset_type.upper()}\n")
            f.write("=" * 60 + "\n")
            f.write(f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(self.rapport.getvalue())

        self.console(f"✅ Analyse terminée!")
        self.console(f"📊 Meilleur modèle: {self.meilleur_modele}")
        self.console(f"📁 Rapport: {nom_rapport}")
        self.console(f"📈 Visualisations: 4 graphiques générés")

    def executer_analyse_complete(self):
        """Exécute l'analyse CRISP-DM complète"""
        self.console("🚀 Démarrage de l'analyse CRISP-DM...")

        try:
            self.charger_donnees()
            self.analyser_donnees()
            self.preprocesser_donnees()
            self.modeliser()
            self.valider()
            self.evaluer()
            self.generer_rapport()

        except Exception as e:
            self.console(f"❌ Erreur: {str(e)}")
            raise

def main():
    """Fonction principale"""
    # Nettoyage des anciens fichiers
    for fichier in ['*.png', '*RAPPORT*.txt']:
        os.system(f'rm -f {fichier}')

    # Sélection du dataset
    if len(sys.argv) > 1:
        dataset = sys.argv[1].lower()
    else:
        print("Datasets disponibles:")
        print("1. credit")
        print("2. revenu")
        dataset = input("Choisissez (credit/revenu): ").lower()

    if dataset not in ['credit', 'revenu']:
        print("❌ Dataset non reconnu. Utilisation de 'credit' par défaut.")
        dataset = 'credit'

    # Exécution de l'analyse
    analyzer = CRISPDMAnalyzer(dataset)
    analyzer.executer_analyse_complete()

if __name__ == "__main__":
    main()