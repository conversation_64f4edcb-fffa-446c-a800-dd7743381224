RAPPORT D'ANALYSE CRISP-DM - REVENU
============================================================
Date: 2025-06-21 22:07:33

=== ANALYSE DU DATASET REVENU ===
Dimensions: (48842, 15)
Variable cible: income
Distribution cible:
income
<=50K    37155
>50K     11687
Name: count, dtype: int64

=== STATISTIQUES DESCRIPTIVES ===
                age        fnlwgt  educational-num  capital-gain  capital-loss  hours-per-week
count  48842.000000  4.884200e+04     48842.000000  48842.000000  48842.000000    48842.000000
mean      38.643585  1.896641e+05        10.078089   1079.067626     87.502314       40.422382
std       13.710510  1.056040e+05         2.570973   7452.019058    403.004552       12.391444
min       17.000000  1.228500e+04         1.000000      0.000000      0.000000        1.000000
25%       28.000000  1.175505e+05         9.000000      0.000000      0.000000       40.000000
50%       37.000000  1.781445e+05        10.000000      0.000000      0.000000       40.000000
75%       48.000000  2.376420e+05        12.000000      0.000000      0.000000       45.000000
max       90.000000  1.490400e+06        16.000000  99999.000000   4356.000000       99.000000

=== INDICATEURS DE POSITION ET DISPERSION ===
age: IQR: 20.000, Asymétrie: 0.558, Aplatissement: -0.184
fnlwgt: IQR: 120091.500, Asymétrie: 1.439, Aplatissement: 6.058
educational-num: IQR: 3.000, Asymétrie: -0.317, Aplatissement: 0.626
capital-gain: IQR: 0.000, Asymétrie: 11.895, Aplatissement: 152.693
capital-loss: IQR: 0.000, Asymétrie: 4.570, Aplatissement: 20.014
hours-per-week: IQR: 5.000, Asymétrie: 0.239, Aplatissement: 2.951

=== VALEURS MANQUANTES ===
workclass         2799
occupation        2809
native-country     857
dtype: int64

Variables numériques: ['age', 'fnlwgt', 'educational-num', 'capital-gain', 'capital-loss', 'hours-per-week']
Variables catégorielles: ['workclass', 'education', 'marital-status', 'occupation', 'relationship', 'race', 'gender', 'native-country']

=== DÉTECTION D'OUTLIERS (méthode IQR) ===
age: 216 outliers (0.4%)
fnlwgt: 1453 outliers (3.0%)
educational-num: 1794 outliers (3.7%)
capital-gain: 4035 outliers (8.3%)
capital-loss: 2282 outliers (4.7%)
hours-per-week: 13496 outliers (27.6%)

=== COMPARAISON CORRÉLATIONS PEARSON vs SPEARMAN ===

=== CORRÉLATIONS AVEC LA CIBLE ===
educational-num: 0.333
age: 0.230
hours-per-week: 0.228
capital-gain: 0.223
capital-loss: 0.148

=== PRÉTRAITEMENT DES DONNÉES ===
Imputation numérique (médiane) pour 6 variables
Justification: Médiane choisie car plus robuste aux outliers détectés
Imputation 'workclass': mode='Private' (5.7% manquant)
Imputation 'occupation': mode='Prof-specialty' (5.8% manquant)
Imputation 'native-country': mode='United-States' (1.8% manquant)
Justification: 'Unknown' si >20% manquant, sinon mode
One-Hot Encoding pour workclass: 7 nouvelles variables
Ordinal Encoding pour education: 16 niveaux ordonnés
One-Hot Encoding pour marital-status: 6 nouvelles variables
Label Encoding pour occupation
One-Hot Encoding pour relationship: 5 nouvelles variables
One-Hot Encoding pour race: 4 nouvelles variables
One-Hot Encoding pour gender: 1 nouvelles variables
Label Encoding pour native-country
MinMaxScaler appliqué à 32 variables
Justification: MinMaxScaler choisi car présence d'outliers significatifs
Split 70/30: Train=(34189, 32), Test=(14653, 32)
SMOTE appliqué: 52016 échantillons équilibrés

=== SUGGESTIONS DE FEATURE ENGINEERING ===
1. Transformation log pour 'fnlwgt' (asymétrie: 1.44)
2. Transformation log pour 'capital-gain' (asymétrie: 11.89)
3. Transformation log pour 'capital-loss' (asymétrie: 4.57)
4. Traitement outliers pour 'capital-gain' (8.3%)
5. Traitement outliers pour 'hours-per-week' (27.6%)
6. Regroupement de catégories pour 'native-country' (41 modalités)

=== ÉVALUATION DES 7 ALGORITHMES ===
Logistic Regression  | F1: 0.8106 | Acc: 0.7985 | Over: 0.0214
Decision Tree        | F1: 0.8111 | Acc: 0.8077 | Over: 0.1923
Random Forest        | F1: 0.8453 | Acc: 0.8439 | Over: 0.1560
KNN                  | F1: 0.7947 | Acc: 0.7842 | Over: 0.1178
SVM                  | F1: 0.7876 | Acc: 0.7721 | Over: 0.0494
Naive Bayes          | F1: 0.6764 | Acc: 0.6528 | Over: 0.1021
Gradient Boosting    | F1: 0.8390 | Acc: 0.8317 | Over: 0.0318

=== VALIDATION CROISÉE ===

--- Validation croisée 5-folds ---
Random Forest        | F1-CV: 0.8972 (±0.0010)
Gradient Boosting    | F1-CV: 0.8616 (±0.0024)
Decision Tree        | F1-CV: 0.8488 (±0.0020)

--- Validation croisée 7-folds ---
Random Forest        | F1-CV: 0.8986 (±0.0026)
Gradient Boosting    | F1-CV: 0.8612 (±0.0021)
Decision Tree        | F1-CV: 0.8561 (±0.0041)

--- Validation croisée 10-folds ---
Random Forest        | F1-CV: 0.9001 (±0.0018)
Gradient Boosting    | F1-CV: 0.8614 (±0.0034)
Decision Tree        | F1-CV: 0.8523 (±0.0056)

=== TABLEAU COMPARATIF DES RÉSULTATS ===
Modèle               F1       Accuracy AUC      Overfitting 
------------------------------------------------------------
Random Forest        0.8453   0.8439   0.9004   0.1560      
Gradient Boosting    0.8390   0.8317   0.9150   0.0318      
Decision Tree        0.8111   0.8077   0.7552   0.1923      
Logistic Regression  0.8106   0.7985   0.8983   0.0214      
KNN                  0.7947   0.7842   0.8355   0.1178      
SVM                  0.7876   0.7721   0.8964   0.0494      
Naive Bayes          0.6764   0.6528   0.8634   0.1021      

=== MEILLEUR MODÈLE ===
Modèle sélectionné: Random Forest
Performance F1: 0.8453

=== RAPPORT DE CLASSIFICATION DÉTAILLÉ ===
Modèle: Random Forest

              precision    recall  f1-score   support

           0       0.90      0.89      0.90     11147
           1       0.67      0.70      0.68      3506

    accuracy                           0.84     14653
   macro avg       0.78      0.79      0.79     14653
weighted avg       0.85      0.84      0.85     14653


=== ANALYSE DE L'OVERFITTING ===
Overfitting moyen: 0.0958
Pas d'overfitting significatif détecté

=== RECOMMANDATIONS ===
1. MODÈLE RECOMMANDÉ: Random Forest
   - Performance F1: 0.8453
   - Accuracy: 0.8439

2. POINTS CLÉS:
   - 7 algorithmes évalués
   - Split 70/30 avec validation croisée
   - Équilibrage SMOTE appliqué
   - Prétraitement complet réalisé

3. AMÉLIORATIONS POSSIBLES:
   - Optimisation des hyperparamètres
   - Feature engineering avancé
   - Ensemble methods
