#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MODULE DE DÉPLOIEMENT - TP1 INF5082
Déploiement et monitoring des modèles de classification
Auteur : Équipe TP1
"""

import os
import json
import pickle
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, Any, List, Tuple
import warnings
warnings.filterwarnings("ignore")

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_deployment.log'),
        logging.StreamHandler()
    ]
)

class ModelDeployment:
    """Classe pour le déploiement et le monitoring des modèles"""
    
    def __init__(self, model_path: str, config_path: str = None):
        """
        Initialise le module de déploiement
        
        Args:
            model_path: Chemin vers le modèle sauvegardé
            config_path: Chemin vers le fichier de configuration
        """
        self.model_path = model_path
        self.config_path = config_path or "deployment_config.json"
        self.model = None
        self.preprocessor = None
        self.config = {}
        self.performance_history = []
        self.alerts = []
        
        # Créer les dossiers nécessaires
        os.makedirs("logs", exist_ok=True)
        os.makedirs("monitoring", exist_ok=True)
        os.makedirs("backups", exist_ok=True)
        
        logging.info("Module de déploiement initialisé")
    
    def load_model(self):
        """Charge le modèle et le préprocesseur"""
        try:
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.preprocessor = model_data['preprocessor']
            self.config = model_data.get('config', {})
            
            logging.info(f"Modèle chargé avec succès depuis {self.model_path}")
            return True
        except Exception as e:
            logging.error(f"Erreur lors du chargement du modèle: {e}")
            return False
    
    def save_model(self, model, preprocessor, config: Dict = None):
        """Sauvegarde le modèle pour déploiement"""
        try:
            model_data = {
                'model': model,
                'preprocessor': preprocessor,
                'config': config or {},
                'deployment_date': datetime.now().isoformat(),
                'version': config.get('version', '1.0') if config else '1.0'
            }
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(model_data, f)
            
            logging.info(f"Modèle sauvegardé pour déploiement: {self.model_path}")
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde: {e}")
            return False
    
    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Effectue une prédiction avec monitoring
        
        Args:
            data: Données d'entrée
            
        Returns:
            Dictionnaire avec prédictions et métadonnées
        """
        if self.model is None:
            raise ValueError("Modèle non chargé. Utilisez load_model() d'abord.")
        
        start_time = datetime.now()
        
        try:
            # Préprocessing
            X_processed = self.preprocessor.transform(data)
            
            # Prédiction
            predictions = self.model.predict(X_processed)
            
            # Probabilités si disponibles
            probabilities = None
            if hasattr(self.model, 'predict_proba'):
                probabilities = self.model.predict_proba(X_processed)
            
            # Calcul du temps de réponse
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Log de la prédiction
            self._log_prediction(len(data), response_time)
            
            return {
                'predictions': predictions.tolist(),
                'probabilities': probabilities.tolist() if probabilities is not None else None,
                'response_time_seconds': response_time,
                'timestamp': datetime.now().isoformat(),
                'model_version': self.config.get('version', '1.0')
            }
            
        except Exception as e:
            logging.error(f"Erreur lors de la prédiction: {e}")
            raise
    
    def _log_prediction(self, n_samples: int, response_time: float):
        """Log des métriques de prédiction"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'n_samples': n_samples,
            'response_time': response_time,
            'model_version': self.config.get('version', '1.0')
        }
        
        # Sauvegarde dans un fichier de log
        log_file = f"logs/predictions_{datetime.now().strftime('%Y%m%d')}.json"
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                logs = json.load(f)
        else:
            logs = []
        
        logs.append(log_entry)
        
        with open(log_file, 'w') as f:
            json.dump(logs, f, indent=2)
    
    def monitor_performance(self, true_labels: List, predictions: List) -> Dict[str, float]:
        """
        Surveille les performances du modèle en production
        
        Args:
            true_labels: Vraies étiquettes
            predictions: Prédictions du modèle
            
        Returns:
            Métriques de performance
        """
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        try:
            metrics = {
                'accuracy': accuracy_score(true_labels, predictions),
                'precision': precision_score(true_labels, predictions, average='weighted'),
                'recall': recall_score(true_labels, predictions, average='weighted'),
                'f1': f1_score(true_labels, predictions, average='weighted'),
                'timestamp': datetime.now().isoformat()
            }
            
            # Ajouter à l'historique
            self.performance_history.append(metrics)
            
            # Vérifier les seuils d'alerte
            self._check_performance_alerts(metrics)
            
            # Sauvegarder les métriques
            self._save_performance_metrics(metrics)
            
            logging.info(f"Métriques de performance calculées: {metrics}")
            return metrics
            
        except Exception as e:
            logging.error(f"Erreur lors du calcul des métriques: {e}")
            raise
    
    def _check_performance_alerts(self, metrics: Dict[str, float]):
        """Vérifie si des alertes doivent être déclenchées"""
        # Seuils d'alerte configurables
        thresholds = {
            'accuracy': 0.8,
            'f1': 0.75,
            'precision': 0.75,
            'recall': 0.75
        }
        
        for metric, value in metrics.items():
            if metric in thresholds and isinstance(value, (int, float)):
                if value < thresholds[metric]:
                    alert = {
                        'timestamp': datetime.now().isoformat(),
                        'type': 'PERFORMANCE_DEGRADATION',
                        'metric': metric,
                        'value': value,
                        'threshold': thresholds[metric],
                        'severity': 'HIGH' if value < thresholds[metric] * 0.9 else 'MEDIUM'
                    }
                    
                    self.alerts.append(alert)
                    logging.warning(f"ALERTE: {metric} = {value:.3f} < seuil {thresholds[metric]}")
    
    def _save_performance_metrics(self, metrics: Dict[str, float]):
        """Sauvegarde les métriques de performance"""
        metrics_file = f"monitoring/performance_{datetime.now().strftime('%Y%m%d')}.json"
        
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                all_metrics = json.load(f)
        else:
            all_metrics = []
        
        all_metrics.append(metrics)
        
        with open(metrics_file, 'w') as f:
            json.dump(all_metrics, f, indent=2)
    
    def generate_monitoring_report(self) -> str:
        """Génère un rapport de monitoring"""
        report = []
        report.append("=" * 60)
        report.append("RAPPORT DE MONITORING - MODÈLE EN PRODUCTION")
        report.append("=" * 60)
        report.append(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Modèle: {self.model_path}")
        report.append(f"Version: {self.config.get('version', '1.0')}")
        report.append("")
        
        # Statistiques des prédictions
        today = datetime.now().strftime('%Y%m%d')
        log_file = f"logs/predictions_{today}.json"
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                logs = json.load(f)
            
            total_predictions = sum(log['n_samples'] for log in logs)
            avg_response_time = np.mean([log['response_time'] for log in logs])
            
            report.append("STATISTIQUES DES PRÉDICTIONS:")
            report.append(f"  - Total prédictions aujourd'hui: {total_predictions}")
            report.append(f"  - Temps de réponse moyen: {avg_response_time:.3f}s")
            report.append(f"  - Nombre d'appels API: {len(logs)}")
        
        # Alertes récentes
        recent_alerts = [a for a in self.alerts 
                        if datetime.fromisoformat(a['timestamp']) > datetime.now() - timedelta(days=1)]
        
        report.append(f"\nALERTES RÉCENTES (24h): {len(recent_alerts)}")
        for alert in recent_alerts[-5:]:  # 5 dernières alertes
            report.append(f"  - {alert['timestamp']}: {alert['type']} - {alert['metric']} = {alert['value']:.3f}")
        
        # Performance récente
        if self.performance_history:
            latest_perf = self.performance_history[-1]
            report.append(f"\nDERNIÈRES PERFORMANCES:")
            report.append(f"  - Accuracy: {latest_perf['accuracy']:.3f}")
            report.append(f"  - F1-Score: {latest_perf['f1']:.3f}")
            report.append(f"  - Precision: {latest_perf['precision']:.3f}")
            report.append(f"  - Recall: {latest_perf['recall']:.3f}")
        
        report_text = "\n".join(report)
        
        # Sauvegarder le rapport
        report_file = f"monitoring/report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        return report_text

    def schedule_retraining(self, performance_threshold: float = 0.75):
        """
        Planifie un re-entraînement si les performances dégradent

        Args:
            performance_threshold: Seuil en dessous duquel déclencher le re-entraînement
        """
        if not self.performance_history:
            return False

        recent_performance = np.mean([p['f1'] for p in self.performance_history[-5:]])

        if recent_performance < performance_threshold:
            alert = {
                'timestamp': datetime.now().isoformat(),
                'type': 'RETRAINING_REQUIRED',
                'recent_f1': recent_performance,
                'threshold': performance_threshold,
                'severity': 'CRITICAL'
            }

            self.alerts.append(alert)
            logging.critical(f"RE-ENTRAÎNEMENT REQUIS: F1 moyen = {recent_performance:.3f}")
            return True

        return False

    def backup_model(self):
        """Crée une sauvegarde du modèle actuel"""
        try:
            backup_path = f"backups/model_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"

            if os.path.exists(self.model_path):
                import shutil
                shutil.copy2(self.model_path, backup_path)
                logging.info(f"Sauvegarde créée: {backup_path}")
                return backup_path

        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde: {e}")

        return None
