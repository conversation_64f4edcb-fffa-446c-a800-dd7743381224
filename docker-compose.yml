version: '3.8'

services:
  # API du modèle
  model-api:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./monitoring:/app/monitoring
      - ./backups:/app/backups
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - model-network

  # Dashboard de monitoring
  monitoring-dashboard:
    build: .
    command: streamlit run monitoring_dashboard.py --server.port=8501 --server.address=0.0.0.0
    ports:
      - "8501:8501"
    volumes:
      - ./logs:/app/logs
      - ./monitoring:/app/monitoring
    depends_on:
      - model-api
    restart: unless-stopped
    networks:
      - model-network

  # Nginx reverse proxy (optionnel)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl  # Pour HTTPS
    depends_on:
      - model-api
      - monitoring-dashboard
    restart: unless-stopped
    networks:
      - model-network

networks:
  model-network:
    driver: bridge

volumes:
  model-data:
  logs-data:
  monitoring-data:
