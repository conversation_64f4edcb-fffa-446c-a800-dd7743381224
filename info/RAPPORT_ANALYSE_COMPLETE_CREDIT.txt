
================================================================================
RAPPORT D'ANALYSE COMPLETE - DATASET CREDIT
================================================================================
Date: 2025-06-21 02:47:10
Méthodologie: CRISP-DM
Algorithmes évalués: 7 (Logistic Regression, Decision Tree, Random Forest, KNN, SVM, Naive Bayes, Gradient Boosting)
Techniques d'équilibrage: 8 (Original, SMOTE, ADASYN, BorderlineSMOTE, RandomOverSampler, RandomUnderSampler, SMOTEENN, SMOTETomek)
Validation croisée: 5, 7, 10 folds
================================================================================


RÉSUMÉ EXÉCUTIF:
- Meilleure technique: SMOTETomek
- Meilleur modèle: Gradient Boosting
- Performance F1-Score: 0.9133
- Accuracy: 0.9130
- AUC: 0.0000

22 fichier(s) supprime(s)

Argument detecte: credit
Fichier './credit.csv' charge avec succes!
Dimensions: (690, 16)

=== ANALYSE DU DATASET CREDIT ===
Apercu des donnees:
   Gender    Age   Debt  Married  BankCustomer  ... DriversLicense       Citizen  ZipCode  Income  Approved
0       1  30.83  0.000        1             1  ...              0       ByBirth      202       0         1
1       0  58.67  4.460        1             1  ...              0       ByBirth       43     560         1
2       0  24.50  0.500        1             1  ...              0       ByBirth      280     824         1
3       1  27.83  1.540        1             1  ...              1       ByBirth      100       3         1
4       1  20.17  5.625        1             1  ...              0  ByOtherMeans      120       0         1

[5 rows x 16 columns]

Informations sur le dataset:
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 690 entries, 0 to 689
Data columns (total 16 columns):
 #   Column          Non-Null Count  Dtype  
---  ------          --------------  -----  
 0   Gender          690 non-null    int64  
 1   Age             690 non-null    float64
 2   Debt            690 non-null    float64
 3   Married         690 non-null    int64  
 4   BankCustomer    690 non-null    int64  
 5   Industry        690 non-null    object 
 6   Ethnicity       690 non-null    object 
 7   YearsEmployed   690 non-null    float64
 8   PriorDefault    690 non-null    int64  
 9   Employed        690 non-null    int64  
 10  CreditScore     690 non-null    int64  
 11  DriversLicense  690 non-null    int64  
 12  Citizen         690 non-null    object 
 13  ZipCode         690 non-null    int64  
 14  Income          690 non-null    int64  
 15  Approved        690 non-null    int64  
dtypes: float64(3), int64(10), object(3)
memory usage: 86.4+ KB


Statistiques descriptives:
           Gender         Age        Debt     Married  ...  DriversLicense      ZipCode         Income    Approved
count  690.000000  690.000000  690.000000  690.000000  ...      690.000000   690.000000     690.000000  690.000000
mean     0.695652   31.514116    4.758725    0.760870  ...        0.457971   180.547826    1017.385507    0.444928
std      0.460464   11.860245    4.978163    0.426862  ...        0.498592   173.970323    5210.102598    0.497318
min      0.000000   13.750000    0.000000    0.000000  ...        0.000000     0.000000       0.000000    0.000000
25%      0.000000   22.670000    1.000000    1.000000  ...        0.000000    60.000000       0.000000    0.000000
50%      1.000000   28.460000    2.750000    1.000000  ...        0.000000   160.000000       5.000000    0.000000
75%      1.000000   37.707500    7.207500    1.000000  ...        1.000000   272.000000     395.500000    1.000000
max      1.000000   80.250000   28.000000    1.000000  ...        1.000000  2000.000000  100000.000000    1.000000

[8 rows x 13 columns]

Valeurs manquantes par colonne:
Gender            0
Age               0
Debt              0
Married           0
BankCustomer      0
Industry          0
Ethnicity         0
YearsEmployed     0
PriorDefault      0
Employed          0
CreditScore       0
DriversLicense    0
Citizen           0
ZipCode           0
Income            0
Approved          0
dtype: int64

Variable cible detectee: 'Approved'

=== DIAGNOSTIC DU DESEQUILIBRE - CREDIT ===
Distribution des classes:
  Classe 0: 383 echantillons (55.5%)
  Classe 1: 307 echantillons (44.5%)

Ratio de desequilibre: 1.25:1
Niveau de desequilibre: EQUILIBRE

================================================================================
PREPROCESSING AVANCE AVEC EDA COMPLET
================================================================================
=== NETTOYAGE DES VALEURS MANQUANTES ===
Valeurs '?' remplacees par np.nan

Valeurs manquantes par colonne:
--------------------------------------------------

=== STATISTIQUES DETAILLEES ===

Statistiques de position et dispersion:
                 Moyenne  Mediane  Ecart-type      IQR      Q1       Q3    Min        Max  Asymetrie  Aplatissement
Gender             0.696     1.00       0.460    1.000   0.000    1.000   0.00       1.00     -0.852         -1.277
Age               31.514    28.46      11.860   15.037  22.670   37.707  13.75      80.25      1.167          1.204
Debt               4.759     2.75       4.978    6.208   1.000    7.208   0.00      28.00      1.489          2.274
Married            0.761     1.00       0.427    0.000   1.000    1.000   0.00       1.00     -1.226         -0.499
BankCustomer       0.764     1.00       0.425    0.000   1.000    1.000   0.00       1.00     -1.245         -0.452
YearsEmployed      2.223     1.00       3.347    2.460   0.165    2.625   0.00      28.50      2.891         11.200
PriorDefault       0.523     1.00       0.500    1.000   0.000    1.000   0.00       1.00     -0.093         -1.997
Employed           0.428     0.00       0.495    1.000   0.000    1.000   0.00       1.00      0.294         -1.919
CreditScore        2.400     0.00       4.863    3.000   0.000    3.000   0.00      67.00      5.153         50.829
DriversLicense     0.458     0.00       0.499    1.000   0.000    1.000   0.00       1.00      0.169         -1.977
ZipCode          180.548   160.00     173.970  212.000  60.000  272.000   0.00    2000.00      2.702         19.224
Income          1017.386     5.00    5210.103  395.500   0.000  395.500   0.00  100000.00     13.141        214.670
Approved           0.445     0.00       0.497    1.000   0.000    1.000   0.00       1.00      0.222         -1.956

=== DETECTION DES OUTLIERS (methode: IQR) ===
Gender               :    0 outliers (  0.0%)
Age                  :   18 outliers (  2.6%)
Debt                 :   17 outliers (  2.5%)
Married              :  165 outliers ( 23.9%)
BankCustomer         :  163 outliers ( 23.6%)
YearsEmployed        :   63 outliers (  9.1%)
PriorDefault         :    0 outliers (  0.0%)
Employed             :    0 outliers (  0.0%)
CreditScore          :   79 outliers ( 11.4%)
DriversLicense       :    0 outliers (  0.0%)
ZipCode              :   12 outliers (  1.7%)
Income               :  113 outliers ( 16.4%)
Approved             :    0 outliers (  0.0%)

=== RECOMMANDATIONS POUR LE TRAITEMENT DES OUTLIERS ===
Age: 2.6% d'outliers
  → Recommandation: Winsorisation ou cap/floor
Debt: 2.5% d'outliers
  → Recommandation: Winsorisation ou cap/floor
Married: ATTENTION - 23.9% d'outliers
  → Recommandation: Transformation log ou suppression
BankCustomer: ATTENTION - 23.6% d'outliers
  → Recommandation: Transformation log ou suppression
YearsEmployed: ATTENTION - 9.1% d'outliers
  → Recommandation: Transformation log ou suppression
CreditScore: ATTENTION - 11.4% d'outliers
  → Recommandation: Transformation log ou suppression
ZipCode: 1.7% d'outliers
  → Recommandation: Winsorisation ou cap/floor
Income: ATTENTION - 16.4% d'outliers
  → Recommandation: Transformation log ou suppression

=== ANALYSE DE CORRELATION AVANCEE ===

Variables les plus correlees avec 'Approved' (Pearson):
  PriorDefault         :  0.720
  Employed             :  0.458
  CreditScore          :  0.406
  YearsEmployed        :  0.322
  Debt                 :  0.206
  BankCustomer         :  0.189
  Married              :  0.181
  Income               :  0.176
  Age                  :  0.164

=== SUGGESTIONS DE FEATURE ENGINEERING ===
Suggestions identifiees:
 1. Transformation log pour 'Age' (asymetrie: 1.17)
 2. Binning pour 'Age' (350 valeurs uniques)
 3. Transformation log pour 'Debt' (asymetrie: 1.49)
 4. Binning pour 'Debt' (215 valeurs uniques)
 5. Transformation log pour 'Married' (asymetrie: -1.23)
 6. Traitement outliers pour 'Married' (23.9%)
 7. Transformation log pour 'BankCustomer' (asymetrie: -1.24)
 8. Traitement outliers pour 'BankCustomer' (23.6%)
 9. Transformation log pour 'YearsEmployed' (asymetrie: 2.89)
10. Binning pour 'YearsEmployed' (132 valeurs uniques)
11. Traitement outliers pour 'YearsEmployed' (9.1%)
12. Transformation log pour 'CreditScore' (asymetrie: 5.15)
13. Traitement outliers pour 'CreditScore' (11.4%)
14. Transformation log pour 'ZipCode' (asymetrie: 2.70)
15. Binning pour 'ZipCode' (170 valeurs uniques)
16. Transformation log pour 'Income' (asymetrie: 13.14)
17. Binning pour 'Income' (240 valeurs uniques)
18. Traitement outliers pour 'Income' (16.4%)
19. Encodage de frequence pour 'Industry'
20. Creation d'interactions entre variables numeriques

============================================================
PREPROCESSING DES DONNEES
============================================================
Variables numeriques detectees: ['Age', 'Debt', 'YearsEmployed', 'CreditScore', 'ZipCode', 'Income']
Variables categorielles detectees: ['Gender', 'Married', 'BankCustomer', 'Industry', 'Ethnicity', 'PriorDefault', 'Employed', 'DriversLicense', 'Citizen']
Imputation numerique (mediane) pour 6 variables
Imputation categorielle (most_frequent) pour 9 variables
  Encodage binaire pour Gender: 1=0, 0=1
  Encodage binaire pour Married: 1=0, 0=1
  Encodage binaire pour BankCustomer: 1=0, 0=1
  One-Hot Encoding pour Industry: 13 nouvelles variables
  One-Hot Encoding pour Ethnicity: 4 nouvelles variables
  Encodage binaire pour PriorDefault: 1=0, 0=1
  Encodage binaire pour Employed: 1=0, 0=1
  Encodage binaire pour DriversLicense: 0=0, 1=1
  One-Hot Encoding pour Citizen: 2 nouvelles variables
StandardScaler applique a 6 variables

================================================================================
RESULTATS DE L'ANALYSE EXPLORATOIRE (EDA)
================================================================================

Suggestions de feature engineering identifiees:
 1. Transformation log pour 'Age' (asymetrie: 1.17)
 2. Binning pour 'Age' (350 valeurs uniques)
 3. Transformation log pour 'Debt' (asymetrie: 1.49)
 4. Binning pour 'Debt' (215 valeurs uniques)
 5. Transformation log pour 'Married' (asymetrie: -1.23)
 6. Traitement outliers pour 'Married' (23.9%)
 7. Transformation log pour 'BankCustomer' (asymetrie: -1.24)
 8. Traitement outliers pour 'BankCustomer' (23.6%)
 9. Transformation log pour 'YearsEmployed' (asymetrie: 2.89)
10. Binning pour 'YearsEmployed' (132 valeurs uniques)
11. Traitement outliers pour 'YearsEmployed' (9.1%)
12. Transformation log pour 'CreditScore' (asymetrie: 5.15)
13. Traitement outliers pour 'CreditScore' (11.4%)
14. Transformation log pour 'ZipCode' (asymetrie: 2.70)
15. Binning pour 'ZipCode' (170 valeurs uniques)
16. Transformation log pour 'Income' (asymetrie: 13.14)
17. Binning pour 'Income' (240 valeurs uniques)
18. Traitement outliers pour 'Income' (16.4%)
19. Encodage de frequence pour 'Industry'
20. Creation d'interactions entre variables numeriques

Outliers detectes dans 13 variables
Variables avec outliers significatifs (>1%):
  Age: 2.6% outliers
  Debt: 2.5% outliers
  Married: 23.9% outliers
  BankCustomer: 23.6% outliers
  YearsEmployed: 9.1% outliers
  CreditScore: 11.4% outliers
  ZipCode: 1.7% outliers
  Income: 16.4% outliers
Dimensions de X_entrainement: (483, 31)
Dimensions de X_test: (207, 31)
Original             | Taille:    483 | Distribution: {0: np.int64(268), 1: np.int64(215)}
SMOTE                | Taille:    536 | Distribution: {0: np.int64(268), 1: np.int64(268)}
ADASYN               | Taille:    517 | Distribution: {0: np.int64(268), 1: np.int64(249)}
BorderlineSMOTE      | Taille:    536 | Distribution: {0: np.int64(268), 1: np.int64(268)}
RandomOverSampler    | Taille:    536 | Distribution: {0: np.int64(268), 1: np.int64(268)}
RandomUnderSampler   | Taille:    430 | Distribution: {0: np.int64(215), 1: np.int64(215)}
SMOTEENN             | Taille:    324 | Distribution: {0: np.int64(149), 1: np.int64(175)}
SMOTETomek           | Taille:    492 | Distribution: {0: np.int64(246), 1: np.int64(246)}
  Cree: credit_Gender_analysis.png
  Cree: credit_Married_analysis.png
  Cree: credit_BankCustomer_analysis.png
  Cree: credit_Industry_analysis.png
  Cree: credit_Ethnicity_analysis.png
  Cree: credit_PriorDefault_analysis.png
  Cree: credit_Employed_analysis.png
  Cree: credit_DriversLicense_analysis.png
  Cree: credit_Citizen_analysis.png

--- Technique: Original ---
  Regression Logistique     | F1: 0.8696 | Acc: 0.8696 | CV: 0.8385±0.0257
  Arbre de Decision         | F1: 0.8506 | Acc: 0.8502 | CV: 0.7805±0.0252
  Random Forest             | F1: 0.8883 | Acc: 0.8889 | CV: 0.8523±0.0233
  KNN                       | F1: 0.8391 | Acc: 0.8406 | CV: 0.7979±0.0416
  SVM (Linear)              | F1: 0.8989 | Acc: 0.8986 | CV: 0.8320±0.0094
  Naive Bayes               | F1: 0.8271 | Acc: 0.8309 | CV: 0.7773±0.0322
  Gradient Boosting         | F1: 0.8988 | Acc: 0.8986 | CV: 0.8384±0.0115

--- Technique: SMOTE ---
  Regression Logistique     | F1: 0.8843 | Acc: 0.8841 | CV: 0.8525±0.0504
  Arbre de Decision         | F1: 0.8266 | Acc: 0.8261 | CV: 0.7982±0.0364
  Random Forest             | F1: 0.9034 | Acc: 0.9034 | CV: 0.8635±0.0520
  KNN                       | F1: 0.8400 | Acc: 0.8406 | CV: 0.8038±0.0582
  SVM (Linear)              | F1: 0.8989 | Acc: 0.8986 | CV: 0.8336±0.0424
  Naive Bayes               | F1: 0.8178 | Acc: 0.8213 | CV: 0.7894±0.0561
  Gradient Boosting         | F1: 0.8843 | Acc: 0.8841 | CV: 0.8488±0.0371

--- Technique: ADASYN ---
  Regression Logistique     | F1: 0.8795 | Acc: 0.8792 | CV: 0.8290±0.0262
  Arbre de Decision         | F1: 0.8070 | Acc: 0.8068 | CV: 0.8257±0.0312
  Random Forest             | F1: 0.8842 | Acc: 0.8841 | CV: 0.8547±0.0112
  KNN                       | F1: 0.8261 | Acc: 0.8261 | CV: 0.7615±0.0553
  SVM (Linear)              | F1: 0.8988 | Acc: 0.8986 | CV: 0.8351±0.0215
  Naive Bayes               | F1: 0.8230 | Acc: 0.8261 | CV: 0.7428±0.0696
  Gradient Boosting         | F1: 0.8892 | Acc: 0.8889 | CV: 0.8207±0.0296

--- Technique: BorderlineSMOTE ---
  Regression Logistique     | F1: 0.8795 | Acc: 0.8792 | CV: 0.8450±0.0443
  Arbre de Decision         | F1: 0.8263 | Acc: 0.8261 | CV: 0.8132±0.0261
  Random Forest             | F1: 0.8939 | Acc: 0.8937 | CV: 0.8749±0.0413
  KNN                       | F1: 0.8398 | Acc: 0.8406 | CV: 0.8003±0.0404
  SVM (Linear)              | F1: 0.9036 | Acc: 0.9034 | CV: 0.8537±0.0212
  Naive Bayes               | F1: 0.8328 | Acc: 0.8357 | CV: 0.7662±0.0480
  Gradient Boosting         | F1: 0.8989 | Acc: 0.8986 | CV: 0.8467±0.0242

--- Technique: RandomOverSampler ---
  Regression Logistique     | F1: 0.8698 | Acc: 0.8696 | CV: 0.8505±0.0337
  Arbre de Decision         | F1: 0.8213 | Acc: 0.8213 | CV: 0.8205±0.0433
  Random Forest             | F1: 0.8742 | Acc: 0.8744 | CV: 0.8749±0.0469
  KNN                       | F1: 0.8356 | Acc: 0.8357 | CV: 0.7778±0.0398
  SVM (Linear)              | F1: 0.8989 | Acc: 0.8986 | CV: 0.8409±0.0253
  Naive Bayes               | F1: 0.8286 | Acc: 0.8309 | CV: 0.7548±0.0464
  Gradient Boosting         | F1: 0.8747 | Acc: 0.8744 | CV: 0.8503±0.0399

--- Technique: RandomUnderSampler ---
  Regression Logistique     | F1: 0.8892 | Acc: 0.8889 | CV: 0.8558±0.0240
  Arbre de Decision         | F1: 0.8458 | Acc: 0.8454 | CV: 0.8046±0.0269
  Random Forest             | F1: 0.9035 | Acc: 0.9034 | CV: 0.8463±0.0087
  KNN                       | F1: 0.8405 | Acc: 0.8406 | CV: 0.8042±0.0342
  SVM (Linear)              | F1: 0.9085 | Acc: 0.9082 | CV: 0.8389±0.0423
  Naive Bayes               | F1: 0.8276 | Acc: 0.8309 | CV: 0.7716±0.0517
  Gradient Boosting         | F1: 0.8989 | Acc: 0.8986 | CV: 0.8487±0.0221

--- Technique: SMOTEENN ---
  Regression Logistique     | F1: 0.8542 | Acc: 0.8551 | CV: 0.9969±0.0062
  Arbre de Decision         | F1: 0.8890 | Acc: 0.8889 | CV: 0.9784±0.0157
  Random Forest             | F1: 0.8939 | Acc: 0.8937 | CV: 0.9908±0.0123
  KNN                       | F1: 0.8254 | Acc: 0.8261 | CV: 0.9722±0.0062
  SVM (Linear)              | F1: 0.8746 | Acc: 0.8744 | CV: 0.9969±0.0062
  Naive Bayes               | F1: 0.8213 | Acc: 0.8261 | CV: 0.9507±0.0179
  Gradient Boosting         | F1: 0.9036 | Acc: 0.9034 | CV: 0.9815±0.0115

--- Technique: SMOTETomek ---
  Regression Logistique     | F1: 0.8747 | Acc: 0.8744 | CV: 0.8821±0.0367
  Arbre de Decision         | F1: 0.8602 | Acc: 0.8599 | CV: 0.8453±0.0132
  Random Forest             | F1: 0.8839 | Acc: 0.8841 | CV: 0.9023±0.0271
  KNN                       | F1: 0.8254 | Acc: 0.8261 | CV: 0.8555±0.0346
  SVM (Linear)              | F1: 0.8940 | Acc: 0.8937 | CV: 0.8678±0.0243
  Naive Bayes               | F1: 0.7634 | Acc: 0.7778 | CV: 0.7636±0.0576
  Gradient Boosting         | F1: 0.9133 | Acc: 0.9130 | CV: 0.8922±0.0175

--- Technique: Class Weight Balanced ---
  Regression Logistique (Balanced) | F1: 0.8747 | Acc: 0.8744 | CV: 0.8428±0.0184
  Arbre de Decision (Balanced) | F1: 0.8600 | Acc: 0.8599 | CV: 0.7869±0.0212
  Random Forest (Balanced)  | F1: 0.8887 | Acc: 0.8889 | CV: 0.8417±0.0236
  SVM Linear (Balanced)     | F1: 0.9132 | Acc: 0.9130 | CV: 0.8325±0.0138

============================================================
RAPPORT DE CLASSIFICATION DÉTAILLÉ - MEILLEUR MODÈLE
============================================================
Modèle: Gradient Boosting
Technique d'équilibrage: SMOTETomek

              precision    recall  f1-score   support

           0       0.96      0.88      0.92       115
           1       0.86      0.96      0.91        92

    accuracy                           0.91       207
   macro avg       0.91      0.92      0.91       207
weighted avg       0.92      0.91      0.91       207


Top 10 des meilleures combinaisons:
--------------------------------------------------------------------------------
Rang Technique            Modele                    F1       Acc      Over    
--------------------------------------------------------------------------------
1    <USER>           <GROUP> Boosting         0.9133   0.9130   0.0544  
2    Class Weight Balanced SVM Linear (Balanced)     0.9132   0.9130   -0.0787 
3    RandomUnderSampler   SVM (Linear)              0.9085   0.9082   -0.0524 
4    BorderlineSMOTE      SVM (Linear)              0.9036   0.9034   -0.0452 
5    SMOTEENN             Gradient Boosting         0.9036   0.9034   0.0966  
6    RandomUnderSampler   Random Forest             0.9035   0.9034   0.0966  
7    SMOTE                Random Forest             0.9034   0.9034   0.0966  
8    BorderlineSMOTE      Gradient Boosting         0.8989   0.8986   0.0324  
9    Original             SVM (Linear)              0.8989   0.8986   -0.0476 
10   SMOTE                SVM (Linear)              0.8989   0.8986   -0.0385 

Performance moyenne par technique d'equilibrage:
----------------------------------------------------------------------
Technique            Moyenne    Ecart-type   Nb modeles  
----------------------------------------------------------------------
Class Weight Balanced 0.8841     0.0227       4           
RandomUnderSampler   0.8734     0.0341       7           
BorderlineSMOTE      0.8678     0.0337       7           
Original             0.8675     0.0292       7           
SMOTEENN             0.8660     0.0331       7           
SMOTE                0.8650     0.0358       7           
SMOTETomek           0.8593     0.0505       7           
ADASYN               0.8583     0.0380       7           
RandomOverSampler    0.8576     0.0291       7           

================================================================================
VALIDATION CROISEE DU MEILLEUR MODELE
================================================================================

=== VALIDATION CROISEE AVEC EQUILIBRAGE ===

Validation croisee 5-folds:
  F1-Score: 0.8321 (±0.0429)
  Accuracy: 0.8322 (±0.0427)
  Variance F1: 0.001842

Validation croisee 7-folds:
  F1-Score: 0.8363 (±0.0564)
  Accuracy: 0.8364 (±0.0562)
  Variance F1: 0.003177

Validation croisee 10-folds:
  F1-Score: 0.8421 (±0.0726)
  Accuracy: 0.8425 (±0.0723)
  Variance F1: 0.005264

=== TABLEAU COMPARATIF COMPLET ===

Tableau comparatif complet (Top 15):
========================================================================================================================
Rang Technique            Modele                    F1       Acc      AUC      Over    

------------------------------------------------------------------------------------------------------------------------
1    <USER>           <GROUP> Boosting         0.9133   0.9130   0.9599   0.0544  

2    Class Weight Balanced SVM Linear (Balanced)     0.9132   0.9130   0.9605   -0.0787 

3    RandomUnderSampler   SVM (Linear)              0.9085   0.9082   0.9574   -0.0524 

4    BorderlineSMOTE      SVM (Linear)              0.9036   0.9034   0.9564   -0.0452 

5    SMOTEENN             Gradient Boosting         0.9036   0.9034   0.9538   0.0966  

6    RandomUnderSampler   Random Forest             0.9035   0.9034   0.9586   0.0966  

7    SMOTE                Random Forest             0.9034   0.9034   0.9526   0.0966  

8    BorderlineSMOTE      Gradient Boosting         0.8989   0.8986   0.9598   0.0324  

9    Original             SVM (Linear)              0.8989   0.8986   0.9559   -0.0476 

10   SMOTE                SVM (Linear)              0.8989   0.8986   0.9573   -0.0385 

11   RandomOverSampler    SVM (Linear)              0.8989   0.8986   0.9544   -0.0515 

12   RandomUnderSampler   Gradient Boosting         0.8989   0.8986   0.9620   0.0317  

13   ADASYN               SVM (Linear)              0.8988   0.8986   0.9602   -0.0456 

14   Original             Gradient Boosting         0.8988   0.8986   0.9580   0.0311  

15   SMOTETomek           SVM (Linear)              0.8940   0.8937   0.9475   0.0067  


=== RECOMMANDATIONS AVANCEES ===

1. ANALYSE DE LA CLASSE MINORITAIRE:
  → Excellente performance detectee (F1 ≥ 0.8)
  → Recommandation: Validation sur donnees externes

2. ANALYSE DE L'OVERFITTING:
  → Overfitting modere detecte (moyenne: 0.052)
  → Recommandation: Surveillance et validation croisee

3. FEATURE ENGINEERING RECOMMANDE:
  Suggestions basees sur l'EDA:
    1. Transformation log pour 'Age' (asymetrie: 1.17)
    2. Binning pour 'Age' (350 valeurs uniques)
    3. Transformation log pour 'Debt' (asymetrie: 1.49)
    4. Binning pour 'Debt' (215 valeurs uniques)
    5. Transformation log pour 'Married' (asymetrie: -1.23)
  Traitement des outliers recommande:
    - Married: 23.9% outliers → Winsorisation ou transformation
    - BankCustomer: 23.6% outliers → Winsorisation ou transformation
    - YearsEmployed: 9.1% outliers → Winsorisation ou transformation
    - CreditScore: 11.4% outliers → Winsorisation ou transformation
    - Income: 16.4% outliers → Winsorisation ou transformation

4. HYPERPARAMETRAGE RECOMMANDE:
  Regression:
    - C: [0.01, 0.1, 1, 10]
    - solver: ['liblinear', 'lbfgs', 'saga']
    - penalty: ['l1', 'l2', 'elasticnet']
  Arbre:
  Random:
    - n_estimators: [100, 200, 500]
    - max_depth: [10, 20, None]
    - min_samples_split: [2, 5, 10]
  KNN:
  SVM:
    - C: [0.1, 1, 10, 100]
    - gamma: ['scale', 'auto', 0.001, 0.01]
    - kernel: ['rbf', 'poly'] si lineaire insuffisant
  Naive:
  Gradient:
    - n_estimators: [100, 200, 500]
    - learning_rate: [0.01, 0.1, 0.2]
    - max_depth: [3, 5, 7]

5. RECOMMANDATIONS CONTEXTUELLES:
  Contexte financier (Credit):
    - Privilegier la precision pour reduire les faux positifs
    - Surveiller le recall pour ne pas manquer les vrais positifs
    - Considerer le cout metier des erreurs de classification

6. PROCHAINES ETAPES PRIORITAIRES:
  1. Validation croisee avec equilibrage sur chaque fold
  2. Optimisation des hyperparametres (GridSearch/RandomSearch)
  3. Feature engineering base sur les suggestions EDA
  4. Validation sur donnees externes
  5. Analyse de l'interpretabilite des modeles
  6. Mise en place du monitoring en production

================================================================================
ANALYSE COMPLETE TERMINEE - CREDIT
================================================================================
Meilleure technique: SMOTETomek
Meilleur modele: Gradient Boosting
Performance (F1-Score): 0.9133
Rapport unique: RAPPORT_FINAL_UNIQUE.txt
Visualisations: 20 graphiques generes
