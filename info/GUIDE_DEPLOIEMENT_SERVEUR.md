# 🌐 GUIDE DE DÉPLOIEMENT SERVEUR - TP1 INF5082

## 🎯 Options de Déploiement

### 1. **Déploiement Local (Test)**
```bash
# Déploiement rapide avec Docker
./deploy.sh local
```

### 2. **Serveur VPS/Dédié (Recommandé)**
```bash
# Déploiement sur votre serveur
./deploy.sh remote
```

### 3. **Cloud Providers**
```bash
# AWS, Google Cloud, Azure
./deploy.sh aws
./deploy.sh gcp
./deploy.sh azure
```

---

## 🖥️ **DÉPLOIEMENT SUR VPS/SERVEUR LINUX**

### Prérequis Serveur
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Minimum 2GB (4GB recommandé)
- **CPU**: 2 cores minimum
- **Stockage**: 20GB minimum
- **R<PERSON><PERSON>**: Ports 80, 443, 5000, 8501 ouverts

### Étape 1: Préparation du Serveur

```bash
# Connexion SSH
ssh ubuntu@YOUR_SERVER_IP

# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation des dépendances
sudo apt install -y curl wget git nginx certbot python3-certbot-nginx

# Installation de Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Installation de Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Redémarrer pour appliquer les groupes
sudo reboot
```

### Étape 2: Déploiement du Code

```bash
# Sur votre machine locale
# Modifier deploy.sh avec vos informations
nano deploy.sh
# Changez REMOTE_HOST="YOUR_SERVER_IP"

# Lancer le déploiement
./deploy.sh remote
```

### Étape 3: Configuration Nginx (Optionnel)

```bash
# Sur le serveur
sudo nano /etc/nginx/sites-available/model-api

# Contenu du fichier:
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /monitoring {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# Activer le site
sudo ln -s /etc/nginx/sites-available/model-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# SSL avec Let's Encrypt (optionnel)
sudo certbot --nginx -d your-domain.com
```

---

## ☁️ **DÉPLOIEMENT CLOUD**

### AWS EC2

#### 1. Création de l'Instance
```bash
# Via AWS CLI
aws ec2 run-instances \
    --image-id ami-0c02fb55956c7d316 \
    --instance-type t3.medium \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --user-data file://user-data.sh
```

#### 2. Script de Démarrage (user-data.sh)
```bash
#!/bin/bash
yum update -y
yum install -y docker git

systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Installation Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Cloner et déployer
cd /opt
git clone https://github.com/YOUR_USERNAME/tp1-inf5082.git
cd tp1-inf5082
docker-compose up -d
```

### Google Cloud Platform

#### 1. App Engine
```bash
# Préparer app.yaml
gcloud app create --region=us-central1
gcloud app deploy

# Ou Compute Engine
gcloud compute instances create model-server \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-medium \
    --metadata-from-file startup-script=startup.sh
```

### Microsoft Azure

#### 1. Container Instances
```bash
# Créer un groupe de ressources
az group create --name tp1-model --location eastus

# Déployer le container
az container create \
    --resource-group tp1-model \
    --name model-api \
    --image your-registry/tp1-model:latest \
    --ports 5000 \
    --dns-name-label tp1-model-api
```

---

## 🔧 **CONFIGURATION AVANCÉE**

### Variables d'Environnement

Créez un fichier `.env` :
```bash
# Configuration de production
FLASK_ENV=production
MODEL_VERSION=1.0
LOG_LEVEL=INFO
MAX_WORKERS=4
TIMEOUT=120

# Base de données (optionnel)
DATABASE_URL=postgresql://user:pass@localhost/modeldb

# Monitoring
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK=https://hooks.slack.com/...
```

### Monitoring Avancé

#### 1. Prometheus + Grafana
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

#### 2. Logs Centralisés
```yaml
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### Sécurité

#### 1. Firewall
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Ou iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

#### 2. SSL/TLS
```bash
# Let's Encrypt
sudo certbot --nginx -d your-domain.com

# Ou certificat personnalisé
sudo mkdir /etc/nginx/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/key.pem \
    -out /etc/nginx/ssl/cert.pem
```

---

## 📊 **MONITORING EN PRODUCTION**

### Health Checks
```bash
# Script de vérification
#!/bin/bash
curl -f http://localhost:5000/health || exit 1
curl -f http://localhost:8501 || exit 1
```

### Alertes Automatiques
```python
# webhook_alerts.py
import requests

def send_slack_alert(message):
    webhook_url = "YOUR_SLACK_WEBHOOK"
    payload = {"text": f"🚨 ALERTE MODÈLE: {message}"}
    requests.post(webhook_url, json=payload)

def send_email_alert(subject, body):
    # Configuration SMTP
    pass
```

### Backup Automatique
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backups/model_backup_$DATE.tar.gz \
    /opt/tp1-inf5082-model/models \
    /opt/tp1-inf5082-model/logs \
    /opt/tp1-inf5082-model/monitoring

# Garder seulement les 7 derniers backups
find /backups -name "model_backup_*.tar.gz" -mtime +7 -delete
```

---

## 🚀 **MISE EN PRODUCTION**

### Checklist Pré-Production
- [ ] Tests de charge réussis
- [ ] Monitoring configuré
- [ ] Alertes testées
- [ ] Backup configuré
- [ ] SSL/TLS activé
- [ ] Firewall configuré
- [ ] Documentation à jour

### Commandes de Gestion
```bash
# Démarrer les services
docker-compose up -d

# Arrêter les services
docker-compose down

# Voir les logs
docker-compose logs -f

# Redémarrer un service
docker-compose restart model-api

# Mise à jour
git pull
docker-compose build
docker-compose up -d
```

### Rollback en Cas de Problème
```bash
# Restaurer depuis backup
cd /opt/tp1-inf5082-model
docker-compose down
tar -xzf /backups/model_backup_YYYYMMDD_HHMMSS.tar.gz
docker-compose up -d
```

---

## 📞 **SUPPORT ET MAINTENANCE**

### Logs Importants
```bash
# Logs de l'application
docker-compose logs model-api

# Logs système
sudo journalctl -u docker
sudo tail -f /var/log/nginx/error.log
```

### Commandes de Diagnostic
```bash
# Vérifier l'état des services
docker-compose ps
systemctl status nginx
systemctl status docker

# Vérifier les ressources
htop
df -h
free -h
```

**🎉 Votre modèle est maintenant prêt pour la production !**
