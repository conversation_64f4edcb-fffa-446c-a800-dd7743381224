
================================================================================
RAPPORT D'ANALYSE COMPLETE - DATASET REVENU
================================================================================
Date: 2025-06-21 02:59:43
Méthodologie: CRISP-DM
Algorithmes évalués: 7 (Logistic Regression, Decision Tree, Random Forest, KNN, SVM, Naive Bayes, Gradient Boosting)
Techniques d'équilibrage: 8 (Original, SMOTE, ADASYN, BorderlineSMOTE, RandomOverSampler, RandomUnderSampler, SMOTEENN, SMOTETomek)
Validation croisée: 5, 7, 10 folds
================================================================================


RÉSUMÉ EXÉCUTIF:
- Meilleure technique: Original
- Meilleur modèle: Random Forest
- Performance F1-Score: 0.8476
- Accuracy: 0.8527
- AUC: 0.0000

22 fichier(s) supprime(s)

Argument detecte: revenu
Fichier './revenu.csv' charge avec succes!
Dimensions: (48842, 15)

=== ANALYSE DU DATASET REVENU ===
Apercu des donnees:
   age  workclass  fnlwgt     education  ...  capital-loss hours-per-week native-country income
0   25    Private  226802          11th  ...             0             40  United-States  <=50K
1   38    Private   89814       HS-grad  ...             0             50  United-States  <=50K
2   28  Local-gov  336951    Assoc-acdm  ...             0             40  United-States   >50K
3   44    Private  160323  Some-college  ...             0             40  United-States   >50K
4   18          ?  103497  Some-college  ...             0             30  United-States  <=50K

[5 rows x 15 columns]

Informations sur le dataset:
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 48842 entries, 0 to 48841
Data columns (total 15 columns):
 #   Column           Non-Null Count  Dtype 
---  ------           --------------  ----- 
 0   age              48842 non-null  int64 
 1   workclass        48842 non-null  object
 2   fnlwgt           48842 non-null  int64 
 3   education        48842 non-null  object
 4   educational-num  48842 non-null  int64 
 5   marital-status   48842 non-null  object
 6   occupation       48842 non-null  object
 7   relationship     48842 non-null  object
 8   race             48842 non-null  object
 9   gender           48842 non-null  object
 10  capital-gain     48842 non-null  int64 
 11  capital-loss     48842 non-null  int64 
 12  hours-per-week   48842 non-null  int64 
 13  native-country   48842 non-null  object
 14  income           48842 non-null  object
dtypes: int64(6), object(9)
memory usage: 5.6+ MB


Statistiques descriptives:
                age        fnlwgt  educational-num  capital-gain  capital-loss  hours-per-week
count  48842.000000  4.884200e+04     48842.000000  48842.000000  48842.000000    48842.000000
mean      38.643585  1.896641e+05        10.078089   1079.067626     87.502314       40.422382
std       13.710510  1.056040e+05         2.570973   7452.019058    403.004552       12.391444
min       17.000000  1.228500e+04         1.000000      0.000000      0.000000        1.000000
25%       28.000000  1.175505e+05         9.000000      0.000000      0.000000       40.000000
50%       37.000000  1.781445e+05        10.000000      0.000000      0.000000       40.000000
75%       48.000000  2.376420e+05        12.000000      0.000000      0.000000       45.000000
max       90.000000  1.490400e+06        16.000000  99999.000000   4356.000000       99.000000

Valeurs manquantes par colonne:
age                0
workclass          0
fnlwgt             0
education          0
educational-num    0
marital-status     0
occupation         0
relationship       0
race               0
gender             0
capital-gain       0
capital-loss       0
hours-per-week     0
native-country     0
income             0
dtype: int64

Variable cible detectee: 'income'

=== DIAGNOSTIC DU DESEQUILIBRE - REVENU ===
Distribution des classes:
  Classe <=50K: 37,155 echantillons (76.1%)
  Classe >50K: 11,687 echantillons (23.9%)

Ratio de desequilibre: 3.18:1
Niveau de desequilibre: DESEQUILIBRE MODERE

================================================================================
PREPROCESSING AVANCE AVEC EDA COMPLET
================================================================================
=== NETTOYAGE DES VALEURS MANQUANTES ===
Valeurs '?' remplacees par np.nan

Valeurs manquantes par colonne:
--------------------------------------------------
workclass            :   2799 (  5.7%)
occupation           :   2809 (  5.8%)
native-country       :    857 (  1.8%)

=== STATISTIQUES DETAILLEES ===

Statistiques de position et dispersion:
                    Moyenne   Mediane  Ecart-type       IQR  ...      Min        Max  Asymetrie  Aplatissement
age                  38.644      37.0      13.711      20.0  ...     17.0       90.0      0.558         -0.184
fnlwgt           189664.135  178144.5  105604.025  120091.5  ...  12285.0  1490400.0      1.439          6.058
educational-num      10.078      10.0       2.571       3.0  ...      1.0       16.0     -0.317          0.626
capital-gain       1079.068       0.0    7452.019       0.0  ...      0.0    99999.0     11.895        152.693
capital-loss         87.502       0.0     403.005       0.0  ...      0.0     4356.0      4.570         20.014
hours-per-week       40.422      40.0      12.391       5.0  ...      1.0       99.0      0.239          2.951

[6 rows x 10 columns]

=== DETECTION DES OUTLIERS (methode: IQR) ===
age                  :  216 outliers (  0.4%)
fnlwgt               : 1453 outliers (  3.0%)
educational-num      : 1794 outliers (  3.7%)
capital-gain         : 4035 outliers (  8.3%)
capital-loss         : 2282 outliers (  4.7%)
hours-per-week       : 13496 outliers ( 27.6%)

=== RECOMMANDATIONS POUR LE TRAITEMENT DES OUTLIERS ===
age: Quelques outliers (216)
  → Recommandation: Conserver ou investigation manuelle
fnlwgt: 3.0% d'outliers
  → Recommandation: Winsorisation ou cap/floor
educational-num: 3.7% d'outliers
  → Recommandation: Winsorisation ou cap/floor
capital-gain: ATTENTION - 8.3% d'outliers
  → Recommandation: Transformation log ou suppression
capital-loss: 4.7% d'outliers
  → Recommandation: Winsorisation ou cap/floor
hours-per-week: ATTENTION - 27.6% d'outliers
  → Recommandation: Transformation log ou suppression

=== ANALYSE DE CORRELATION AVANCEE ===

=== SUGGESTIONS DE FEATURE ENGINEERING ===
Suggestions identifiees:
 1. Binning pour 'age' (74 valeurs uniques)
 2. Transformation log pour 'fnlwgt' (asymetrie: 1.44)
 3. Binning pour 'fnlwgt' (28523 valeurs uniques)
 4. Transformation log pour 'capital-gain' (asymetrie: 11.89)
 5. Binning pour 'capital-gain' (123 valeurs uniques)
 6. Traitement outliers pour 'capital-gain' (8.3%)
 7. Transformation log pour 'capital-loss' (asymetrie: 4.57)
 8. Binning pour 'capital-loss' (99 valeurs uniques)
 9. Binning pour 'hours-per-week' (96 valeurs uniques)
10. Traitement outliers pour 'hours-per-week' (27.6%)
11. Encodage de frequence pour 'education'
12. Encodage de frequence pour 'occupation'
13. Regroupement de categories pour 'native-country' (41 categories)
14. Encodage de frequence pour 'native-country'
15. Creation d'interactions entre variables numeriques

============================================================
PREPROCESSING DES DONNEES
============================================================
Variables numeriques detectees: ['age', 'fnlwgt', 'educational-num', 'capital-gain', 'capital-loss', 'hours-per-week']
Variables categorielles detectees: ['workclass', 'education', 'marital-status', 'occupation', 'relationship', 'race', 'gender', 'native-country']
Imputation numerique (mediane) pour 6 variables
Imputation categorielle (most_frequent) pour 8 variables
Encodage ordinal pour: ['education']
  education: ['Preschool', '1st-4th', '5th-6th', '7th-8th', '9th', '10th', '11th', '12th', 'HS-grad', 'Some-college', 'Assoc-voc', 'Assoc-acdm', 'Bachelors', 'Masters', 'Prof-school', 'Doctorate']
  One-Hot Encoding pour workclass: 7 nouvelles variables
  One-Hot Encoding pour marital-status: 6 nouvelles variables
  One-Hot Encoding pour occupation: 13 nouvelles variables
  One-Hot Encoding pour relationship: 5 nouvelles variables
  One-Hot Encoding pour race: 4 nouvelles variables
  Encodage binaire pour gender: Male=0, Female=1
  One-Hot Encoding pour native-country: 40 nouvelles variables
StandardScaler applique a 6 variables
  Encodage de la variable cible income: <=50K=0, >50K=1

================================================================================
RESULTATS DE L'ANALYSE EXPLORATOIRE (EDA)
================================================================================

Suggestions de feature engineering identifiees:
 1. Binning pour 'age' (74 valeurs uniques)
 2. Transformation log pour 'fnlwgt' (asymetrie: 1.44)
 3. Binning pour 'fnlwgt' (28523 valeurs uniques)
 4. Transformation log pour 'capital-gain' (asymetrie: 11.89)
 5. Binning pour 'capital-gain' (123 valeurs uniques)
 6. Traitement outliers pour 'capital-gain' (8.3%)
 7. Transformation log pour 'capital-loss' (asymetrie: 4.57)
 8. Binning pour 'capital-loss' (99 valeurs uniques)
 9. Binning pour 'hours-per-week' (96 valeurs uniques)
10. Traitement outliers pour 'hours-per-week' (27.6%)
11. Encodage de frequence pour 'education'
12. Encodage de frequence pour 'occupation'
13. Regroupement de categories pour 'native-country' (41 categories)
14. Encodage de frequence pour 'native-country'
15. Creation d'interactions entre variables numeriques

Outliers detectes dans 6 variables
Variables avec outliers significatifs (>1%):
  fnlwgt: 3.0% outliers
  educational-num: 3.7% outliers
  capital-gain: 8.3% outliers
  capital-loss: 4.7% outliers
  hours-per-week: 27.6% outliers
Dimensions de X_entrainement: (14000, 83)
Dimensions de X_test: (6000, 83)
Original             | Taille: 14,000 | Distribution: {0: np.int64(10650), 1: np.int64(3350)}
SMOTE                | Taille: 21,300 | Distribution: {0: np.int64(10650), 1: np.int64(10650)}
ADASYN               | Taille: 21,129 | Distribution: {0: np.int64(10650), 1: np.int64(10479)}
BorderlineSMOTE      | Taille: 21,300 | Distribution: {0: np.int64(10650), 1: np.int64(10650)}
RandomOverSampler    | Taille: 21,300 | Distribution: {0: np.int64(10650), 1: np.int64(10650)}
RandomUnderSampler   | Taille:  6,700 | Distribution: {0: np.int64(3350), 1: np.int64(3350)}
SMOTEENN             | Taille: 15,972 | Distribution: {0: np.int64(7259), 1: np.int64(8713)}
SMOTETomek           | Taille: 20,868 | Distribution: {0: np.int64(10434), 1: np.int64(10434)}
  Cree: revenu_workclass_analysis.png
  Cree: revenu_education_analysis.png
  Cree: revenu_marital-status_analysis.png
  Cree: revenu_occupation_analysis.png
  Cree: revenu_relationship_analysis.png
  Cree: revenu_race_analysis.png
  Cree: revenu_gender_analysis.png
  Cree: revenu_native-country_analysis.png

--- Technique: Original ---
  Regression Logistique     | F1: 0.8422 | Acc: 0.8480 | CV: 0.8422±0.0047
  Arbre de Decision         | F1: 0.8065 | Acc: 0.8057 | CV: 0.8076±0.0055
  Random Forest             | F1: 0.8476 | Acc: 0.8527 | CV: 0.8418±0.0025
  KNN                       | F1: 0.8236 | Acc: 0.8267 | CV: 0.8207±0.0020
  SVM (Linear)              | F1: 0.7894 | Acc: 0.7770 | CV: 0.8110±0.0180
  Naive Bayes               | F1: 0.4772 | Acc: 0.4743 | CV: 0.4326±0.0548
  Gradient Boosting         | F1: 0.8472 | Acc: 0.8570 | CV: 0.8502±0.0050

--- Technique: SMOTE ---
  Regression Logistique     | F1: 0.8175 | Acc: 0.8082 | CV: 0.8407±0.0241
  Arbre de Decision         | F1: 0.8046 | Acc: 0.8012 | CV: 0.8463±0.0382
  Random Forest             | F1: 0.8397 | Acc: 0.8380 | CV: 0.8994±0.0336
  KNN                       | F1: 0.7959 | Acc: 0.7852 | CV: 0.8670±0.0149
  SVM (Linear)              | F1: 0.7200 | Acc: 0.6988 | CV: 0.7587±0.0673
  Naive Bayes               | F1: 0.4727 | Acc: 0.4708 | CV: 0.6101±0.0128
  Gradient Boosting         | F1: 0.8232 | Acc: 0.8130 | CV: 0.8525±0.0195

--- Technique: ADASYN ---
  Regression Logistique     | F1: 0.8000 | Acc: 0.7877 | CV: 0.8200±0.0173
  Arbre de Decision         | F1: 0.7941 | Acc: 0.7900 | CV: 0.8307±0.0254
  Random Forest             | F1: 0.8301 | Acc: 0.8268 | CV: 0.8879±0.0220
  KNN                       | F1: 0.7837 | Acc: 0.7702 | CV: 0.8449±0.0112
  SVM (Linear)              | F1: 0.7144 | Acc: 0.6918 | CV: 0.6322±0.0738
  Naive Bayes               | F1: 0.3151 | Acc: 0.3593 | CV: 0.4780±0.0262
  Gradient Boosting         | F1: 0.8029 | Acc: 0.7893 | CV: 0.8292±0.0103

--- Technique: BorderlineSMOTE ---
  Regression Logistique     | F1: 0.7951 | Acc: 0.7818 | CV: 0.8245±0.0185
  Arbre de Decision         | F1: 0.7957 | Acc: 0.7908 | CV: 0.8324±0.0563
  Random Forest             | F1: 0.8351 | Acc: 0.8325 | CV: 0.8982±0.0322
  KNN                       | F1: 0.7880 | Acc: 0.7752 | CV: 0.8641±0.0140
  SVM (Linear)              | F1: 0.6966 | Acc: 0.6728 | CV: 0.7686±0.0296
  Naive Bayes               | F1: 0.3221 | Acc: 0.3637 | CV: 0.4756±0.0321
  Gradient Boosting         | F1: 0.8011 | Acc: 0.7872 | CV: 0.8364±0.0135

--- Technique: RandomOverSampler ---
  Regression Logistique     | F1: 0.8140 | Acc: 0.8025 | CV: 0.8192±0.0062
  Arbre de Decision         | F1: 0.8100 | Acc: 0.8108 | CV: 0.9170±0.0138
  Random Forest             | F1: 0.8410 | Acc: 0.8408 | CV: 0.9334±0.0087
  KNN                       | F1: 0.7817 | Acc: 0.7680 | CV: 0.8342±0.0113
  SVM (Linear)              | F1: 0.7183 | Acc: 0.6960 | CV: 0.7533±0.0288
  Naive Bayes               | F1: 0.4694 | Acc: 0.4683 | CV: 0.6002±0.0198
  Gradient Boosting         | F1: 0.8192 | Acc: 0.8075 | CV: 0.8304±0.0050

--- Technique: RandomUnderSampler ---
  Regression Logistique     | F1: 0.8107 | Acc: 0.7987 | CV: 0.8219±0.0085
  Arbre de Decision         | F1: 0.7681 | Acc: 0.7527 | CV: 0.7654±0.0059
  Random Forest             | F1: 0.8146 | Acc: 0.8035 | CV: 0.8164±0.0062
  KNN                       | F1: 0.7837 | Acc: 0.7685 | CV: 0.7933±0.0098
  SVM (Linear)              | F1: 0.7904 | Acc: 0.7753 | CV: 0.8163±0.0124
  Naive Bayes               | F1: 0.5484 | Acc: 0.5330 | CV: 0.6354±0.0221
  Gradient Boosting         | F1: 0.8135 | Acc: 0.8010 | CV: 0.8311±0.0136

--- Technique: SMOTEENN ---
  Regression Logistique     | F1: 0.7901 | Acc: 0.7748 | CV: 0.9344±0.0064
  Arbre de Decision         | F1: 0.7928 | Acc: 0.7798 | CV: 0.9486±0.0114
  Random Forest             | F1: 0.8137 | Acc: 0.8020 | CV: 0.9735±0.0081
  KNN                       | F1: 0.7769 | Acc: 0.7603 | CV: 0.9735±0.0012
  SVM (Linear)              | F1: 0.7814 | Acc: 0.7650 | CV: 0.9334±0.0072
  Naive Bayes               | F1: 0.4837 | Acc: 0.4797 | CV: 0.7200±0.0182
  Gradient Boosting         | F1: 0.7933 | Acc: 0.7780 | CV: 0.9401±0.0053

--- Technique: SMOTETomek ---
  Regression Logistique     | F1: 0.8165 | Acc: 0.8070 | CV: 0.8473±0.0230
  Arbre de Decision         | F1: 0.8075 | Acc: 0.8037 | CV: 0.8606±0.0339
  Random Forest             | F1: 0.8360 | Acc: 0.8343 | CV: 0.9093±0.0305
  KNN                       | F1: 0.7978 | Acc: 0.7870 | CV: 0.8758±0.0129
  SVM (Linear)              | F1: 0.7129 | Acc: 0.6902 | CV: 0.8085±0.0232
  Naive Bayes               | F1: 0.4737 | Acc: 0.4717 | CV: 0.6174±0.0122
  Gradient Boosting         | F1: 0.8242 | Acc: 0.8138 | CV: 0.8592±0.0192

--- Technique: Class Weight Balanced ---
  Regression Logistique (Balanced) | F1: 0.8120 | Acc: 0.8002 | CV: 0.8151±0.0064
  Arbre de Decision (Balanced) | F1: 0.8071 | Acc: 0.8082 | CV: 0.8079±0.0063
  Random Forest (Balanced)  | F1: 0.8443 | Acc: 0.8498 | CV: 0.8431±0.0050
  SVM Linear (Balanced)     | F1: 0.7467 | Acc: 0.7267 | CV: 0.7611±0.0161

============================================================
RAPPORT DE CLASSIFICATION DÉTAILLÉ - MEILLEUR MODÈLE
============================================================
Modèle: Random Forest
Technique d'équilibrage: Original

              precision    recall  f1-score   support

           0       0.88      0.93      0.91      4564
           1       0.73      0.60      0.66      1436

    accuracy                           0.85      6000
   macro avg       0.81      0.77      0.78      6000
weighted avg       0.85      0.85      0.85      6000


Top 10 des meilleures combinaisons:
--------------------------------------------------------------------------------
Rang Technique            Modele                    F1       Acc      Over    
--------------------------------------------------------------------------------
1    <USER>             <GROUP> Forest             0.8476   0.8527   0.1467  
2    Original             Gradient Boosting         0.8472   0.8570   0.0064  
3    Class Weight Balanced Random Forest (Balanced)  0.8443   0.8498   0.1494  
4    Original             Regression Logistique     0.8422   0.8480   0.0014  
5    RandomOverSampler    Random Forest             0.8410   0.8408   0.1592  
6    SMOTE                Random Forest             0.8397   0.8380   0.1620  
7    SMOTETomek           Random Forest             0.8360   0.8343   0.1655  
8    BorderlineSMOTE      Random Forest             0.8351   0.8325   0.1674  
9    ADASYN               Random Forest             0.8301   0.8268   0.1731  
10   SMOTETomek           Gradient Boosting         0.8242   0.8138   0.0504  

Performance moyenne par technique d'equilibrage:
----------------------------------------------------------------------
Technique            Moyenne    Ecart-type   Nb modeles  
----------------------------------------------------------------------
Class Weight Balanced 0.8025     0.0407       4           
Original             0.7762     0.1337       7           
RandomUnderSampler   0.7613     0.0955       7           
SMOTE                0.7534     0.1296       7           
SMOTETomek           0.7527     0.1295       7           
RandomOverSampler    0.7505     0.1301       7           
SMOTEENN             0.7474     0.1169       7           
ADASYN               0.7201     0.1821       7           
BorderlineSMOTE      0.7191     0.1801       7           

================================================================================
VALIDATION CROISEE DU MEILLEUR MODELE
================================================================================

=== VALIDATION CROISEE AVEC EQUILIBRAGE ===

Validation croisee 5-folds:
  F1-Score: 0.8468 (±0.0036)
  Accuracy: 0.8514 (±0.0042)
  Variance F1: 0.000013

Validation croisee 7-folds:
  F1-Score: 0.8432 (±0.0066)
  Accuracy: 0.8486 (±0.0061)
  Variance F1: 0.000043

Validation croisee 10-folds:
  F1-Score: 0.8444 (±0.0045)
  Accuracy: 0.8495 (±0.0043)
  Variance F1: 0.000021

=== TABLEAU COMPARATIF COMPLET ===

Tableau comparatif complet (Top 15):
========================================================================================================================
Rang Technique            Modele                    F1       Acc      AUC      Over    

------------------------------------------------------------------------------------------------------------------------
1    <USER>             <GROUP> Forest             0.8476   0.8527   0.8996   0.1467  

2    Original             Gradient Boosting         0.8472   0.8570   0.9132   0.0064  

3    Class Weight Balanced Random Forest (Balanced)  0.8443   0.8498   0.8978   0.1494  

4    Original             Regression Logistique     0.8422   0.8480   0.9017   0.0014  

5    RandomOverSampler    Random Forest             0.8410   0.8408   0.8956   0.1592  

6    SMOTE                Random Forest             0.8397   0.8380   0.8938   0.1620  

7    SMOTETomek           Random Forest             0.8360   0.8343   0.8945   0.1655  

8    BorderlineSMOTE      Random Forest             0.8351   0.8325   0.8900   0.1674  

9    ADASYN               Random Forest             0.8301   0.8268   0.8912   0.1731  

10   SMOTETomek           Gradient Boosting         0.8242   0.8138   0.9060   0.0504  

11   Original             KNN                       0.8236   0.8267   0.8522   0.0507  

12   SMOTE                Gradient Boosting         0.8232   0.8130   0.9057   0.0457  

13   RandomOverSampler    Gradient Boosting         0.8192   0.8075   0.9116   0.0266  

14   SMOTE                Regression Logistique     0.8175   0.8082   0.8919   0.0363  

15   SMOTETomek           Regression Logistique     0.8165   0.8070   0.8918   0.0445  


=== RECOMMANDATIONS AVANCEES ===

1. ANALYSE DE LA CLASSE MINORITAIRE:
  → Excellente performance detectee (F1 ≥ 0.8)
  → Recommandation: Validation sur donnees externes

2. ANALYSE DE L'OVERFITTING:
  → Overfitting eleve detecte (moyenne: 0.121)
  → Recommandations:
    - Augmenter la regularisation (C plus faible pour SVM/LogReg)
    - Reduire max_depth pour arbres
    - Augmenter min_samples_split pour Random Forest
    - Utiliser Dropout pour reseaux de neurones

3. FEATURE ENGINEERING RECOMMANDE:
  Suggestions basees sur l'EDA:
    1. Binning pour 'age' (74 valeurs uniques)
    2. Transformation log pour 'fnlwgt' (asymetrie: 1.44)
    3. Binning pour 'fnlwgt' (28523 valeurs uniques)
    4. Transformation log pour 'capital-gain' (asymetrie: 11.89)
    5. Binning pour 'capital-gain' (123 valeurs uniques)
  Traitement des outliers recommande:
    - capital-gain: 8.3% outliers → Winsorisation ou transformation
    - hours-per-week: 27.6% outliers → Winsorisation ou transformation

4. HYPERPARAMETRAGE RECOMMANDE:
  Regression:
    - C: [0.01, 0.1, 1, 10]
    - solver: ['liblinear', 'lbfgs', 'saga']
    - penalty: ['l1', 'l2', 'elasticnet']
  Arbre:
  Random:
    - n_estimators: [100, 200, 500]
    - max_depth: [10, 20, None]
    - min_samples_split: [2, 5, 10]
  KNN:
  SVM:
    - C: [0.1, 1, 10, 100]
    - gamma: ['scale', 'auto', 0.001, 0.01]
    - kernel: ['rbf', 'poly'] si lineaire insuffisant
  Naive:
  Gradient:
    - n_estimators: [100, 200, 500]
    - learning_rate: [0.01, 0.1, 0.2]
    - max_depth: [3, 5, 7]

5. RECOMMANDATIONS CONTEXTUELLES:
  Contexte socio-economique (Revenu):
    - F1-Score equilibre recommande
    - Attention aux biais demographiques
    - Validation sur differents groupes demographiques

6. PROCHAINES ETAPES PRIORITAIRES:
  1. Validation croisee avec equilibrage sur chaque fold
  2. Optimisation des hyperparametres (GridSearch/RandomSearch)
  3. Feature engineering base sur les suggestions EDA
  4. Validation sur donnees externes
  5. Analyse de l'interpretabilite des modeles
  6. Mise en place du monitoring en production

================================================================================
ANALYSE COMPLETE TERMINEE - REVENU
================================================================================
Meilleure technique: Original
Meilleur modele: Random Forest
Performance (F1-Score): 0.8476
Rapport unique: RAPPORT_FINAL_UNIQUE.txt
Visualisations: 20 graphiques generes
