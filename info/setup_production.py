#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Configuration automatique pour la production - TP1 INF5082
Script d'aide au déploiement sur différentes plateformes
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def create_production_config():
    """Crée la configuration de production"""
    
    print("🔧 Configuration de Production - TP1 INF5082")
    print("=" * 50)
    
    # Collecter les informations
    config = {}
    
    print("\n📋 Informations de déploiement:")
    config['project_name'] = input("Nom du projet [tp1-inf5082]: ") or "tp1-inf5082"
    config['domain'] = input("Nom de domaine (optionnel): ") or ""
    config['email'] = input("Email pour les alertes: ") or ""
    
    print("\n🌐 Type de déploiement:")
    print("1. Serveur VPS/Dédié")
    print("2. AWS EC2")
    print("3. Google Cloud")
    print("4. Azure")
    print("5. Heroku")
    
    deployment_type = input("Choisissez (1-5): ") or "1"
    
    if deployment_type == "1":
        config['deployment'] = 'vps'
        config['server_ip'] = input("IP du serveur: ")
        config['server_user'] = input("Utilisateur SSH [ubuntu]: ") or "ubuntu"
        
    elif deployment_type == "2":
        config['deployment'] = 'aws'
        config['aws_region'] = input("Région AWS [us-east-1]: ") or "us-east-1"
        config['instance_type'] = input("Type d'instance [t3.medium]: ") or "t3.medium"
        
    elif deployment_type == "3":
        config['deployment'] = 'gcp'
        config['gcp_project'] = input("ID du projet GCP: ")
        config['gcp_region'] = input("Région GCP [us-central1]: ") or "us-central1"
        
    elif deployment_type == "4":
        config['deployment'] = 'azure'
        config['azure_resource_group'] = input("Groupe de ressources: ")
        config['azure_location'] = input("Localisation [eastus]: ") or "eastus"
        
    elif deployment_type == "5":
        config['deployment'] = 'heroku'
        config['heroku_app_name'] = input("Nom de l'app Heroku: ")
    
    # Sauvegarder la configuration
    with open('production_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ Configuration sauvegardée dans production_config.json")
    
    return config

def generate_deployment_files(config):
    """Génère les fichiers de déploiement selon la configuration"""
    
    deployment = config.get('deployment', 'vps')
    
    if deployment == 'vps':
        generate_vps_files(config)
    elif deployment == 'aws':
        generate_aws_files(config)
    elif deployment == 'gcp':
        generate_gcp_files(config)
    elif deployment == 'azure':
        generate_azure_files(config)
    elif deployment == 'heroku':
        generate_heroku_files(config)

def generate_vps_files(config):
    """Génère les fichiers pour déploiement VPS"""
    
    # Script de déploiement personnalisé
    deploy_script = f"""#!/bin/bash
# Script de déploiement automatique pour {config['project_name']}

set -e

SERVER_IP="{config['server_ip']}"
SERVER_USER="{config['server_user']}"
PROJECT_PATH="/opt/{config['project_name']}"

echo "🚀 Déploiement sur VPS $SERVER_IP"

# Préparer le modèle
python save_best_model.py credit

# Copier les fichiers
rsync -avz --exclude='.git' --exclude='__pycache__' \\
      ./ $SERVER_USER@$SERVER_IP:$PROJECT_PATH/

# Installation sur le serveur
ssh $SERVER_USER@$SERVER_IP << 'EOF'
cd {config['project_name']}

# Installer Docker si nécessaire
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# Installer Docker Compose
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Lancer les services
docker-compose down || true
docker-compose build
docker-compose up -d

echo "✅ Déploiement terminé!"
EOF

echo "🌐 Votre modèle est accessible à:"
echo "   API: http://$SERVER_IP:5000"
echo "   Monitoring: http://$SERVER_IP:8501"
"""
    
    with open('deploy_vps.sh', 'w') as f:
        f.write(deploy_script)
    
    os.chmod('deploy_vps.sh', 0o755)
    print("✅ Script deploy_vps.sh créé")

def generate_aws_files(config):
    """Génère les fichiers pour AWS"""
    
    # CloudFormation template
    cloudformation = {
        "AWSTemplateFormatVersion": "2010-09-09",
        "Description": f"Infrastructure pour {config['project_name']}",
        "Parameters": {
            "InstanceType": {
                "Type": "String",
                "Default": config['instance_type'],
                "Description": "Type d'instance EC2"
            },
            "KeyName": {
                "Type": "AWS::EC2::KeyPair::KeyName",
                "Description": "Nom de la paire de clés EC2"
            }
        },
        "Resources": {
            "SecurityGroup": {
                "Type": "AWS::EC2::SecurityGroup",
                "Properties": {
                    "GroupDescription": "Security group pour le modèle",
                    "SecurityGroupIngress": [
                        {
                            "IpProtocol": "tcp",
                            "FromPort": 22,
                            "ToPort": 22,
                            "CidrIp": "0.0.0.0/0"
                        },
                        {
                            "IpProtocol": "tcp",
                            "FromPort": 80,
                            "ToPort": 80,
                            "CidrIp": "0.0.0.0/0"
                        },
                        {
                            "IpProtocol": "tcp",
                            "FromPort": 443,
                            "ToPort": 443,
                            "CidrIp": "0.0.0.0/0"
                        }
                    ]
                }
            },
            "EC2Instance": {
                "Type": "AWS::EC2::Instance",
                "Properties": {
                    "ImageId": "ami-0c02fb55956c7d316",
                    "InstanceType": {"Ref": "InstanceType"},
                    "KeyName": {"Ref": "KeyName"},
                    "SecurityGroups": [{"Ref": "SecurityGroup"}],
                    "UserData": {
                        "Fn::Base64": {
                            "Fn::Join": ["", [
                                "#!/bin/bash\n",
                                "yum update -y\n",
                                "yum install -y docker git\n",
                                "systemctl start docker\n",
                                "systemctl enable docker\n",
                                "usermod -a -G docker ec2-user\n",
                                "curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)\" -o /usr/local/bin/docker-compose\n",
                                "chmod +x /usr/local/bin/docker-compose\n"
                            ]]
                        }
                    }
                }
            }
        },
        "Outputs": {
            "InstanceId": {
                "Description": "ID de l'instance EC2",
                "Value": {"Ref": "EC2Instance"}
            },
            "PublicIP": {
                "Description": "IP publique de l'instance",
                "Value": {"Fn::GetAtt": ["EC2Instance", "PublicIp"]}
            }
        }
    }
    
    with open('aws-infrastructure.json', 'w') as f:
        json.dump(cloudformation, f, indent=2)
    
    print("✅ Template CloudFormation aws-infrastructure.json créé")

def generate_gcp_files(config):
    """Génère les fichiers pour Google Cloud"""
    
    # app.yaml pour App Engine
    app_yaml = f"""runtime: python311

service: {config['project_name']}

env_variables:
  FLASK_ENV: production

automatic_scaling:
  min_instances: 1
  max_instances: 10

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

handlers:
- url: /.*
  script: auto
"""
    
    with open('app.yaml', 'w') as f:
        f.write(app_yaml)
    
    # Script de déploiement GCP
    deploy_gcp = f"""#!/bin/bash
# Déploiement sur Google Cloud Platform

set -e

PROJECT_ID="{config['gcp_project']}"
REGION="{config['gcp_region']}"

echo "🚀 Déploiement sur Google Cloud"

# Configurer le projet
gcloud config set project $PROJECT_ID

# Préparer le modèle
python save_best_model.py credit

# Déployer sur App Engine
gcloud app deploy --region=$REGION

echo "✅ Déploiement terminé!"
echo "🌐 URL: https://$PROJECT_ID.appspot.com"
"""
    
    with open('deploy_gcp.sh', 'w') as f:
        f.write(deploy_gcp)
    
    os.chmod('deploy_gcp.sh', 0o755)
    print("✅ Fichiers GCP créés (app.yaml, deploy_gcp.sh)")

def generate_azure_files(config):
    """Génère les fichiers pour Azure"""
    
    # Script de déploiement Azure
    deploy_azure = f"""#!/bin/bash
# Déploiement sur Microsoft Azure

set -e

RESOURCE_GROUP="{config['azure_resource_group']}"
LOCATION="{config['azure_location']}"
APP_NAME="{config['project_name']}"

echo "🚀 Déploiement sur Azure"

# Créer le groupe de ressources
az group create --name $RESOURCE_GROUP --location $LOCATION

# Créer le plan App Service
az appservice plan create \\
    --name $APP_NAME-plan \\
    --resource-group $RESOURCE_GROUP \\
    --sku B1 \\
    --is-linux

# Créer l'application web
az webapp create \\
    --resource-group $RESOURCE_GROUP \\
    --plan $APP_NAME-plan \\
    --name $APP_NAME \\
    --runtime "PYTHON|3.11"

# Déployer le code
az webapp deployment source config-zip \\
    --resource-group $RESOURCE_GROUP \\
    --name $APP_NAME \\
    --src deployment.zip

echo "✅ Déploiement terminé!"
echo "🌐 URL: https://$APP_NAME.azurewebsites.net"
"""
    
    with open('deploy_azure.sh', 'w') as f:
        f.write(deploy_azure)
    
    os.chmod('deploy_azure.sh', 0o755)
    print("✅ Script deploy_azure.sh créé")

def generate_heroku_files(config):
    """Génère les fichiers pour Heroku"""
    
    # Procfile
    procfile = """web: gunicorn model_api:app --bind 0.0.0.0:$PORT --workers 4
"""
    
    with open('Procfile', 'w') as f:
        f.write(procfile)
    
    # runtime.txt
    runtime = "python-3.11.0"
    
    with open('runtime.txt', 'w') as f:
        f.write(runtime)
    
    # Script de déploiement Heroku
    deploy_heroku = f"""#!/bin/bash
# Déploiement sur Heroku

set -e

APP_NAME="{config['heroku_app_name']}"

echo "🚀 Déploiement sur Heroku"

# Créer l'application
heroku create $APP_NAME

# Configurer les variables d'environnement
heroku config:set FLASK_ENV=production --app $APP_NAME

# Préparer le modèle
python save_best_model.py credit

# Déployer
git add .
git commit -m "Deploy to Heroku"
git push heroku main

echo "✅ Déploiement terminé!"
echo "🌐 URL: https://$APP_NAME.herokuapp.com"
"""
    
    with open('deploy_heroku.sh', 'w') as f:
        f.write(deploy_heroku)
    
    os.chmod('deploy_heroku.sh', 0o755)
    print("✅ Fichiers Heroku créés (Procfile, runtime.txt, deploy_heroku.sh)")

def main():
    """Fonction principale"""
    
    print("🚀 CONFIGURATION DE PRODUCTION - TP1 INF5082")
    print("=" * 60)
    
    # Vérifier si un fichier de config existe
    if os.path.exists('production_config.json'):
        use_existing = input("Configuration existante trouvée. L'utiliser? (y/n): ")
        if use_existing.lower() == 'y':
            with open('production_config.json', 'r') as f:
                config = json.load(f)
        else:
            config = create_production_config()
    else:
        config = create_production_config()
    
    # Générer les fichiers de déploiement
    print(f"\n🔧 Génération des fichiers pour {config['deployment']}...")
    generate_deployment_files(config)
    
    # Instructions finales
    print(f"\n🎉 Configuration terminée!")
    print(f"📁 Fichiers générés dans le répertoire courant")
    
    deployment = config['deployment']
    if deployment == 'vps':
        print(f"▶️ Lancez: ./deploy_vps.sh")
    elif deployment == 'aws':
        print(f"▶️ Déployez avec: aws cloudformation create-stack --stack-name {config['project_name']} --template-body file://aws-infrastructure.json")
    elif deployment == 'gcp':
        print(f"▶️ Lancez: ./deploy_gcp.sh")
    elif deployment == 'azure':
        print(f"▶️ Lancez: ./deploy_azure.sh")
    elif deployment == 'heroku':
        print(f"▶️ Lancez: ./deploy_heroku.sh")
    
    print(f"\n📖 Consultez GUIDE_DEPLOIEMENT_SERVEUR.md pour plus de détails")

if __name__ == "__main__":
    main()
