# 🚀 GUIDE DE DÉPLOIEMENT - TP1 INF5082

## Vue d'ensemble

Ce guide vous accompagne dans le déploiement complet de vos modèles de classification selon la méthodologie CRISP-DM, étape 6 : **Déploiement**.

## 📋 Table des Matières

1. [Préparation du Modèle](#1-préparation-du-modèle)
2. [Déploiement de l'API](#2-déploiement-de-lapi)
3. [Monitoring et Surveillance](#3-monitoring-et-surveillance)
4. [Maintenance et Re-entraînement](#4-maintenance-et-re-entraînement)
5. [Documentation Utilisateur](#5-documentation-utilisateur)

---

## 1. Préparation du Modèle

### 1.1 Sélection du Meilleur Modèle

```bash
# Sélectionner et sauvegarder le meilleur modèle pour le dataset Crédit
python save_best_model.py credit

# Ou pour le dataset Revenu
python save_best_model.py revenu
```

**Résultats attendus :**
- ✅ `models/best_model.pkl` - Modèle prêt pour production
- ✅ `models/model_config.json` - Configuration du modèle
- ✅ `models/README.md` - Documentation du modèle

### 1.2 Vérification du Modèle

```python
from deployment_module import ModelDeployment

# Charger et tester le modèle
deployment = ModelDeployment("models/best_model.pkl")
success = deployment.load_model()

if success:
    print("✅ Modèle chargé avec succès")
else:
    print("❌ Erreur de chargement")
```

---

## 2. Déploiement de l'API

### 2.1 Installation des Dépendances

```bash
# Installer Flask pour l'API
pip install flask

# Installer Streamlit pour le dashboard (optionnel)
pip install streamlit plotly
```

### 2.2 Lancement de l'API

```bash
# Démarrer le serveur API
python model_api.py
```

**L'API sera accessible à :** `http://localhost:5000`

### 2.3 Endpoints Disponibles

| Endpoint | Méthode | Description |
|----------|---------|-------------|
| `/` | GET | Interface web principale |
| `/health` | GET | Vérification de l'état du service |
| `/predict` | POST | Prédiction sur nouvelles données |
| `/feedback` | POST | Envoi de feedback pour monitoring |
| `/monitoring` | GET | Rapport de monitoring |

### 2.4 Test de l'API

**Exemple de requête de prédiction :**

```bash
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{
    "data": [{
      "Gender": 1,
      "Age": 30,
      "Debt": 2.5,
      "Married": 1,
      "BankCustomer": 1,
      "Industry": "Energy",
      "Ethnicity": "White",
      "YearsEmployed": 2.0,
      "PriorDefault": 0,
      "Employed": 1,
      "CreditScore": 5,
      "DriversLicense": 1,
      "Citizen": "ByBirth",
      "ZipCode": "00100",
      "Income": 1000
    }]
  }'
```

---

## 3. Monitoring et Surveillance

### 3.1 Dashboard de Monitoring

```bash
# Lancer le dashboard de monitoring
streamlit run monitoring_dashboard.py
```

**Dashboard accessible à :** `http://localhost:8501`

### 3.2 Métriques Surveillées

- **Performance** : Accuracy, F1-Score, Precision, Recall
- **Opérationnelles** : Temps de réponse, Nombre de prédictions
- **Système** : Statut API, Alertes, Logs

### 3.3 Alertes Automatiques

Le système génère automatiquement des alertes pour :

- 🔴 **Performance dégradée** : F1 < 75%
- 🟠 **Temps de réponse élevé** : > 2 secondes
- 🟡 **Volume anormal** : Pic ou chute des prédictions

### 3.4 Logs et Historique

```
logs/
├── predictions_20241221.json    # Logs des prédictions
├── model_deployment.log         # Logs système
└── ...

monitoring/
├── performance_20241221.json    # Métriques de performance
├── report_20241221_143022.txt   # Rapports générés
└── ...
```

---

## 4. Maintenance et Re-entraînement

### 4.1 Surveillance Continue

```python
# Vérifier les performances
deployment = ModelDeployment("models/best_model.pkl")
deployment.load_model()

# Simuler du feedback
true_labels = [1, 0, 1, 1, 0]
predictions = [1, 0, 0, 1, 0]

metrics = deployment.monitor_performance(true_labels, predictions)
print(f"Accuracy actuelle: {metrics['accuracy']:.3f}")
```

### 4.2 Déclenchement du Re-entraînement

Le re-entraînement est déclenché automatiquement si :
- F1-Score moyen < 75% sur les 5 dernières évaluations
- Drift détecté dans les données
- Alerte critique générée

### 4.3 Sauvegarde et Rollback

```python
# Créer une sauvegarde avant mise à jour
backup_path = deployment.backup_model()
print(f"Sauvegarde créée: {backup_path}")

# En cas de problème, restaurer depuis la sauvegarde
# cp backups/model_backup_20241221_143022.pkl models/best_model.pkl
```

---

## 5. Documentation Utilisateur

### 5.1 Guide d'Utilisation de l'API

**Pour les Développeurs :**

```python
import requests
import json

# Configuration
API_URL = "http://localhost:5000"

# Fonction de prédiction
def predict_credit_approval(customer_data):
    response = requests.post(
        f"{API_URL}/predict",
        json={"data": [customer_data]},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result['result']['predictions'][0]
    else:
        raise Exception(f"Erreur API: {response.text}")

# Exemple d'utilisation
customer = {
    "Gender": 1, "Age": 35, "Debt": 5.0,
    "Married": 1, "BankCustomer": 1,
    # ... autres variables
}

prediction = predict_credit_approval(customer)
print(f"Prédiction: {'Approuvé' if prediction == 1 else 'Refusé'}")
```

### 5.2 Interface Web

L'interface web à `http://localhost:5000` permet :
- ✅ Test interactif des prédictions
- ✅ Visualisation du statut du service
- ✅ Documentation des endpoints
- ✅ Exemples de requêtes

### 5.3 Formation des Utilisateurs

**Points clés à retenir :**

1. **Format des données** : Respecter exactement le schéma d'entrée
2. **Interprétation** : 1 = Approuvé, 0 = Refusé
3. **Limites** : Le modèle est entraîné sur des données historiques
4. **Feedback** : Signaler les erreurs pour améliorer le modèle

---

## 📊 Plan de Monitoring

### KPIs Principaux

| Métrique | Seuil Critique | Fréquence de Vérification |
|----------|----------------|---------------------------|
| Accuracy | < 80% | Temps réel |
| F1-Score | < 75% | Temps réel |
| Temps de réponse | > 2s | Temps réel |
| Disponibilité | < 99% | Continue |

### Rapports Automatiques

- **Quotidien** : Résumé des performances
- **Hebdomadaire** : Analyse des tendances
- **Mensuel** : Rapport complet avec recommandations

---

## 🔧 Dépannage

### Problèmes Courants

**1. Modèle non chargé**
```bash
# Vérifier l'existence du fichier
ls -la models/best_model.pkl

# Régénérer si nécessaire
python save_best_model.py credit
```

**2. API non accessible**
```bash
# Vérifier le processus
ps aux | grep model_api.py

# Redémarrer si nécessaire
python model_api.py
```

**3. Performances dégradées**
```bash
# Vérifier les logs
tail -f logs/model_deployment.log

# Analyser les métriques
python -c "from deployment_module import ModelDeployment; d = ModelDeployment('models/best_model.pkl'); print(d.generate_monitoring_report())"
```

---

## 📞 Support

Pour toute question ou problème :

1. **Consulter les logs** : `logs/model_deployment.log`
2. **Vérifier le monitoring** : Dashboard Streamlit
3. **Tester l'API** : Interface web à `http://localhost:5000`

---

**🎉 Félicitations ! Votre modèle est maintenant déployé en production selon les meilleures pratiques CRISP-DM !**
