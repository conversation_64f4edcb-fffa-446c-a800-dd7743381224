# Dockerfile pour déploiement du modèle TP1 INF5082
FROM python:3.11-slim

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers de requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Copier le code de l'application
COPY . .

# Créer les dossiers nécessaires
RUN mkdir -p logs monitoring backups models

# Exposer le port
EXPOSE 5000

# Variables d'environnement
ENV FLASK_APP=model_api.py
ENV FLASK_ENV=production
ENV PYTHONPATH=/app

# Commande de démarrage avec Gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "120", "model_api:app"]
